import aiosqlite
import json
from typing import Dict, List, Optional, Any
import config

class Database:
    def __init__(self, db_path: str = config.DATABASE_PATH):
        self.db_path = db_path

    async def init_db(self):
        """Initialize database tables"""
        async with aiosqlite.connect(self.db_path) as db:
            # Users table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT,
                    first_name TEXT,
                    auth_data TEXT,
                    notifications_enabled BOOLEAN DEFAULT 1,
                    arbitrage_threshold REAL DEFAULT 0.15,
                    min_price REAL DEFAULT 1.0,
                    max_price REAL DEFAULT 1000.0,
                    preferred_assets TEXT DEFAULT "TON",
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # Watchlist table
            await db.execute('''
                CREATE TABLE IF NOT EXISTS watchlist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    gift_name TEXT,
                    model TEXT,
                    backdrop TEXT,
                    symbol TEXT,
                    max_price REAL,
                    asset TEXT DEFAULT "TON",
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')

            # Notifications history
            await db.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    gift_id INTEGER,
                    gift_name TEXT,
                    price REAL,
                    notification_type TEXT,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')

            # Price history for analytics
            await db.execute('''
                CREATE TABLE IF NOT EXISTS price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    gift_name TEXT,
                    model TEXT,
                    backdrop TEXT,
                    symbol TEXT,
                    price REAL,
                    asset TEXT,
                    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # User settings
            await db.execute('''
                CREATE TABLE IF NOT EXISTS user_settings (
                    user_id INTEGER PRIMARY KEY,
                    auto_buy_enabled BOOLEAN DEFAULT 0,
                    auto_buy_budget REAL DEFAULT 0,
                    notification_cooldown INTEGER DEFAULT 300,
                    preferred_sort TEXT DEFAULT "price_asc",
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')

            await db.commit()

    async def add_user(self, user_id: int, username: str = None, first_name: str = None) -> bool:
        """Add or update user"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT OR REPLACE INTO users (user_id, username, first_name, last_active)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ''', (user_id, username, first_name))
                
                # Initialize user settings
                await db.execute('''
                    INSERT OR IGNORE INTO user_settings (user_id)
                    VALUES (?)
                ''', (user_id,))
                
                await db.commit()
                return True
        except Exception as e:
            print(f"Error adding user: {e}")
            return False

    async def get_user(self, user_id: int) -> Optional[Dict]:
        """Get user data"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute('''
                    SELECT u.*, s.* FROM users u
                    LEFT JOIN user_settings s ON u.user_id = s.user_id
                    WHERE u.user_id = ?
                ''', (user_id,))
                row = await cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            print(f"Error getting user: {e}")
            return None

    async def update_auth_data(self, user_id: int, auth_data: str) -> bool:
        """Update user's auth data"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    UPDATE users SET auth_data = ?, last_active = CURRENT_TIMESTAMP
                    WHERE user_id = ?
                ''', (auth_data, user_id))
                await db.commit()
                return True
        except Exception as e:
            print(f"Error updating auth data: {e}")
            return False

    async def add_to_watchlist(self, user_id: int, gift_name: str = None, model: str = None,
                              backdrop: str = None, symbol: str = None, max_price: float = None,
                              asset: str = "TON") -> bool:
        """Add item to watchlist"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT INTO watchlist (user_id, gift_name, model, backdrop, symbol, max_price, asset)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (user_id, gift_name, model, backdrop, symbol, max_price, asset))
                await db.commit()
                return True
        except Exception as e:
            print(f"Error adding to watchlist: {e}")
            return False

    async def get_watchlist(self, user_id: int) -> List[Dict]:
        """Get user's watchlist"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                cursor = await db.execute('''
                    SELECT * FROM watchlist WHERE user_id = ? ORDER BY created_at DESC
                ''', (user_id,))
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            print(f"Error getting watchlist: {e}")
            return []

    async def remove_from_watchlist(self, user_id: int, watchlist_id: int) -> bool:
        """Remove item from watchlist"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    DELETE FROM watchlist WHERE id = ? AND user_id = ?
                ''', (watchlist_id, user_id))
                await db.commit()
                return True
        except Exception as e:
            print(f"Error removing from watchlist: {e}")
            return False

    async def add_notification(self, user_id: int, gift_id: int, gift_name: str,
                              price: float, notification_type: str) -> bool:
        """Add notification to history"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT INTO notifications (user_id, gift_id, gift_name, price, notification_type)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, gift_id, gift_name, price, notification_type))
                await db.commit()
                return True
        except Exception as e:
            print(f"Error adding notification: {e}")
            return False

    async def record_price(self, gift_name: str, model: str, backdrop: str,
                          symbol: str, price: float, asset: str) -> bool:
        """Record price for analytics"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT INTO price_history (gift_name, model, backdrop, symbol, price, asset)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (gift_name, model, backdrop, symbol, price, asset))
                await db.commit()
                return True
        except Exception as e:
            print(f"Error recording price: {e}")
            return False

    async def get_price_history(self, gift_name: str = None, days: int = 7) -> List[Dict]:
        """Get price history for analytics"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                db.row_factory = aiosqlite.Row
                if gift_name:
                    cursor = await db.execute('''
                        SELECT * FROM price_history 
                        WHERE gift_name = ? AND recorded_at >= datetime('now', '-{} days')
                        ORDER BY recorded_at DESC
                    '''.format(days), (gift_name,))
                else:
                    cursor = await db.execute('''
                        SELECT * FROM price_history 
                        WHERE recorded_at >= datetime('now', '-{} days')
                        ORDER BY recorded_at DESC
                    '''.format(days))
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            print(f"Error getting price history: {e}")
            return []

    async def update_user_settings(self, user_id: int, **kwargs) -> bool:
        """Update user settings"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # Build dynamic update query
                fields = []
                values = []
                for key, value in kwargs.items():
                    fields.append(f"{key} = ?")
                    values.append(value)
                
                if fields:
                    query = f"UPDATE user_settings SET {', '.join(fields)} WHERE user_id = ?"
                    values.append(user_id)
                    await db.execute(query, values)
                    await db.commit()
                return True
        except Exception as e:
            print(f"Error updating user settings: {e}")
            return False

# Global database instance
db = Database()
