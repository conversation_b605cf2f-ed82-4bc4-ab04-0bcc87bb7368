import asyncio
import logging
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes
from telegram.constants import ParseMode

import config
from database import db
from tonnel_api import tonnel_api
from arbitrage import arbitrage_analyzer

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TonnelBot:
    def __init__(self):
        self.app = Application.builder().token(config.BOT_TOKEN).build()
        self.setup_handlers()

    def setup_handlers(self):
        """Setup bot command and callback handlers"""
        # Commands
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("help", self.help_command))
        self.app.add_handler(CommandHandler("auth", self.auth_command))
        self.app.add_handler(CommandHandler("balance", self.balance_command))
        self.app.add_handler(CommandHandler("search", self.search_command))
        self.app.add_handler(CommandHandler("arbitrage", self.arbitrage_command))
        self.app.add_handler(CommandHandler("watchlist", self.watchlist_command))
        self.app.add_handler(CommandHandler("market", self.market_command))
        self.app.add_handler(CommandHandler("trends", self.trends_command))
        self.app.add_handler(CommandHandler("mygifts", self.my_gifts_command))
        self.app.add_handler(CommandHandler("auctions", self.auctions_command))
        
        # Callback handlers
        self.app.add_handler(CallbackQueryHandler(self.button_callback))
        
        # Message handlers
        self.app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user = update.effective_user
        await db.add_user(user.id, user.username, user.first_name)
        
        welcome_text = f"""
🎁 **Добро пожаловать в Tonnel Trading Bot!**

Привет, {user.first_name}! Я помогу тебе торговать подарками на Tonnel Marketplace.

**Основные функции:**
🔍 Поиск подарков и аукционов
📊 Анализ арбитражных возможностей  
📈 Отслеживание трендов цен
⚡ Уведомления о выгодных сделках
💰 Управление балансом и портфолио
🎯 Вотчлист для отслеживания цен

**Для начала работы:**
1. Настрой авторизацию: /auth
2. Проверь баланс: /balance
3. Найди подарки: /search
4. Найди арбитраж: /arbitrage

Используй /help для полного списка команд.
        """
        
        keyboard = [
            [InlineKeyboardButton("🔑 Настроить авторизацию", callback_data="setup_auth")],
            [InlineKeyboardButton("📊 Обзор рынка", callback_data="market_overview")],
            [InlineKeyboardButton("🎯 Арбитраж", callback_data="find_arbitrage")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(welcome_text, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_text = """
🤖 **Команды бота:**

**Основные:**
/start - Запуск бота
/help - Справка по командам
/auth - Настройка авторизации

**Торговля:**
/search <название> - Поиск подарков
/arbitrage - Поиск арбитражных возможностей
/market - Обзор рынка
/trends <название> - Анализ трендов

**Аккаунт:**
/balance - Проверка баланса
/mygifts - Мои подарки
/watchlist - Управление вотчлистом

**Аукционы:**
/auctions - Поиск аукционов

**Примеры использования:**
`/search toy bear` - найти Toy Bear
`/trends lunar snake` - тренды Lunar Snake
`/auth YOUR_AUTH_DATA` - настроить авторизацию

**Получение Auth Data:**
1. Зайди на market.tonnel.network
2. Открой консоль (F12)
3. Application → Local Storage → web-initData
4. Скопируй значение и отправь: /auth ЗНАЧЕНИЕ
        """
        
        await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)

    async def auth_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /auth command"""
        user_id = update.effective_user.id
        
        if not context.args:
            await update.message.reply_text(
                "🔑 **Настройка авторизации**\n\n"
                "Для работы с твоим аккаунтом нужны данные авторизации.\n\n"
                "**Как получить:**\n"
                "1. Зайди на market.tonnel.network\n"
                "2. Войди в аккаунт\n"
                "3. Открой консоль браузера (Ctrl+Shift+C)\n"
                "4. Application → Local Storage → web-initData\n"
                "5. Скопируй значение\n"
                "6. Отправь: `/auth ТВОИ_ДАННЫЕ`\n\n"
                "⚠️ Данные будут сохранены безопасно и использованы только для API запросов.",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        auth_data = " ".join(context.args)
        
        # Test auth data
        try:
            user_info = await asyncio.to_thread(tonnel_api.get_user_info, auth_data)
            
            if user_info.get("status") == "error":
                await update.message.reply_text(
                    "❌ Неверные данные авторизации. Проверь правильность копирования."
                )
                return
            
            # Save auth data
            await db.update_auth_data(user_id, auth_data)
            
            balance = user_info.get("balance", 0)
            name = user_info.get("name", "Неизвестно")
            
            await update.message.reply_text(
                f"✅ **Авторизация успешна!**\n\n"
                f"👤 Имя: {name}\n"
                f"💰 Баланс: {balance} TON\n\n"
                f"Теперь ты можешь использовать все функции бота!",
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка при проверке авторизации: {str(e)}"
            )

    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /balance command"""
        user_id = update.effective_user.id
        user_data = await db.get_user(user_id)
        
        if not user_data or not user_data.get("auth_data"):
            await update.message.reply_text(
                "❌ Сначала настрой авторизацию: /auth"
            )
            return
        
        try:
            user_info = await asyncio.to_thread(
                tonnel_api.get_user_info, 
                user_data["auth_data"]
            )
            
            if user_info.get("status") == "error":
                await update.message.reply_text(
                    "❌ Ошибка получения данных. Проверь авторизацию: /auth"
                )
                return
            
            balance_text = f"""
💰 **Твой баланс:**

🪙 TON: {user_info.get('balance', 0):.6f}
💎 USDT: {user_info.get('usdtBalance', 0):.6f}  
🎯 TONNEL: {user_info.get('tonnelBalance', 0):.6f}

👤 **Профиль:**
Имя: {user_info.get('name', 'Неизвестно')}
Реферрер: {user_info.get('referrer', 'Нет')}
Внутренние покупки: {'Включены' if user_info.get('transferGift') else 'Отключены'}

📝 **Memo:** `{user_info.get('memo', 'Не установлено')}`
            """
            
            keyboard = [
                [InlineKeyboardButton("🔄 Обновить", callback_data="refresh_balance")],
                [InlineKeyboardButton("💸 Вывести средства", callback_data="withdraw_funds")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                balance_text, 
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка: {str(e)}"
            )

    async def search_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /search command"""
        if not context.args:
            await update.message.reply_text(
                "🔍 **Поиск подарков**\n\n"
                "Использование: `/search <название подарка>`\n\n"
                "Примеры:\n"
                "`/search toy bear`\n"
                "`/search lunar snake`\n"
                "`/search winter wreath`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        gift_name = " ".join(context.args)
        
        try:
            # Search for gifts
            gifts_response = await asyncio.to_thread(
                tonnel_api.get_gifts,
                gift_name=gift_name,
                limit=10,
                sort="price_asc"
            )
            
            if gifts_response.get("status") == "error":
                await update.message.reply_text(
                    f"❌ Ошибка поиска: {gifts_response.get('error')}"
                )
                return
            
            gifts = gifts_response.get("data", [])
            
            if not gifts:
                await update.message.reply_text(
                    f"🔍 Подарки с названием '{gift_name}' не найдены."
                )
                return
            
            # Format results
            results_text = f"🎁 **Найдено подарков: {len(gifts)}**\n\n"
            
            for i, gift in enumerate(gifts[:5], 1):
                price = gift.get("price", 0)
                model = gift.get("model", "Неизвестно")
                backdrop = gift.get("backdrop", "Неизвестно")
                symbol = gift.get("symbol", "Неизвестно")
                gift_num = gift.get("gift_num", "Неизвестно")
                
                results_text += f"""
**{i}. {gift.get('name', 'Неизвестно')}**
💰 Цена: {price} {gift.get('asset', 'TON')}
🎨 Модель: {model}
🖼 Фон: {backdrop}
🔸 Символ: {symbol}
🔢 Номер: {gift_num}
                """
            
            if len(gifts) > 5:
                results_text += f"\n... и еще {len(gifts) - 5} подарков"
            
            keyboard = [
                [InlineKeyboardButton("📊 Анализ цен", callback_data=f"analyze_{gift_name}")],
                [InlineKeyboardButton("⭐ Добавить в вотчлист", callback_data=f"watchlist_add_{gift_name}")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                results_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка поиска: {str(e)}"
            )

    async def arbitrage_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /arbitrage command"""
        await update.message.reply_text("🔍 Ищу арбитражные возможности...")
        
        try:
            opportunities = await arbitrage_analyzer.find_arbitrage_opportunities()
            
            if not opportunities:
                await update.message.reply_text(
                    "📊 Арбитражных возможностей не найдено.\n"
                    "Попробуй позже или измени параметры поиска."
                )
                return
            
            results_text = f"💎 **Найдено возможностей: {len(opportunities)}**\n\n"
            
            for i, opp in enumerate(opportunities[:3], 1):
                gift = opp["gift"]
                profit_margin = opp["profit_margin"] * 100
                potential_profit = opp["potential_profit"]
                
                results_text += f"""
**{i}. {gift.get('name', 'Неизвестно')}**
🎨 {gift.get('model', 'Неизвестно')}
💰 Цена: {opp['lowest_price']} TON
📈 Средняя: {opp['average_price']:.2f} TON
🚀 Прибыль: {profit_margin:.1f}% ({potential_profit:.2f} TON)
📊 Глубина: {opp['market_depth']} лотов
🔢 ID: {gift.get('gift_id')}
                """
            
            keyboard = [
                [InlineKeyboardButton("🔄 Обновить", callback_data="refresh_arbitrage")],
                [InlineKeyboardButton("⚙️ Настройки", callback_data="arbitrage_settings")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                results_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка анализа: {str(e)}"
            )

    async def market_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /market command"""
        await update.message.reply_text("📊 Анализирую рынок...")
        
        try:
            user_id = update.effective_user.id
            user_data = await db.get_user(user_id)
            auth_data = user_data.get("auth_data", "") if user_data else ""
            
            summary = await arbitrage_analyzer.get_market_summary(auth_data)
            
            if "error" in summary:
                await update.message.reply_text(
                    f"❌ Ошибка анализа рынка: {summary['error']}"
                )
                return
            
            market_text = f"""
📊 **Обзор рынка Tonnel**

📈 **Статистика:**
Всего лотов: {summary['total_listings']}
Средняя цена: {summary['average_price']} TON
Медианная цена: {summary['median_price']} TON
Мин. цена: {summary['min_price']} TON
Макс. цена: {summary['max_price']} TON

💎 **Арбитраж:**
Возможностей: {summary['arbitrage_opportunities']}
            """
            
            if summary.get('top_opportunity'):
                top = summary['top_opportunity']
                profit = top['profit_margin'] * 100
                market_text += f"""
🚀 **Лучшая возможность:**
{top['gift']['name']} - {profit:.1f}% прибыли
                """
            
            # Asset distribution
            if summary.get('asset_distribution'):
                market_text += "\n💰 **По валютам:**\n"
                for asset, count in summary['asset_distribution'].items():
                    market_text += f"{asset}: {count} лотов\n"
            
            market_text += f"\n🕐 Обновлено: {datetime.now().strftime('%H:%M:%S')}"
            
            keyboard = [
                [InlineKeyboardButton("🔄 Обновить", callback_data="refresh_market")],
                [InlineKeyboardButton("🎯 Арбитраж", callback_data="find_arbitrage")],
                [InlineKeyboardButton("📈 Недооцененные", callback_data="undervalued")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                market_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка: {str(e)}"
            )

    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        if data == "setup_auth":
            await query.edit_message_text(
                "🔑 **Настройка авторизации**\n\n"
                "Отправь команду: `/auth ТВОИ_ДАННЫЕ`\n\n"
                "Как получить данные:\n"
                "1. Зайди на market.tonnel.network\n"
                "2. Войди в аккаунт\n"
                "3. F12 → Application → Local Storage → web-initData\n"
                "4. Скопируй значение",
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif data == "market_overview":
            await self.market_command(update, context)
        
        elif data == "find_arbitrage":
            await self.arbitrage_command(update, context)
        
        elif data == "refresh_balance":
            await self.balance_command(update, context)
        
        elif data == "refresh_market":
            await self.market_command(update, context)
        
        elif data == "refresh_arbitrage":
            await self.arbitrage_command(update, context)

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        text = update.message.text.lower()
        
        if "auth" in text and len(text) > 50:
            # Looks like auth data
            context.args = [update.message.text]
            await self.auth_command(update, context)
        else:
            await update.message.reply_text(
                "🤖 Используй команды для работы с ботом.\n"
                "Список команд: /help"
            )

    async def my_gifts_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /mygifts command"""
        user_id = update.effective_user.id
        user_data = await db.get_user(user_id)

        if not user_data or not user_data.get("auth_data"):
            await update.message.reply_text(
                "❌ Сначала настрой авторизацию: /auth"
            )
            return

        try:
            # Get user's gifts
            my_gifts_response = await asyncio.to_thread(
                tonnel_api.my_gifts,
                listed=False,
                auth_data=user_data["auth_data"]
            )

            if my_gifts_response.get("status") == "error":
                await update.message.reply_text(
                    f"❌ Ошибка получения подарков: {my_gifts_response.get('error')}"
                )
                return

            gifts = my_gifts_response.get("data", [])

            if not gifts:
                await update.message.reply_text(
                    "🎁 У тебя пока нет подарков в портфолио."
                )
                return

            # Format gifts list
            gifts_text = f"🎁 **Твои подарки ({len(gifts)}):**\n\n"

            total_value = 0
            for i, gift in enumerate(gifts[:10], 1):  # Show first 10
                name = gift.get("name", "Неизвестно")
                model = gift.get("model", "Неизвестно")
                price = gift.get("price", 0)
                status = gift.get("status", "unknown")

                status_emoji = "🔴" if status == "forsale" else "🟢"

                gifts_text += f"""
**{i}. {name}**
🎨 {model}
💰 {price} TON
{status_emoji} {status}
                """

                total_value += price

            if len(gifts) > 10:
                gifts_text += f"\n... и еще {len(gifts) - 10} подарков"

            gifts_text += f"\n💎 **Общая стоимость: {total_value:.2f} TON**"

            keyboard = [
                [InlineKeyboardButton("📊 Анализ портфолио", callback_data="portfolio_analysis")],
                [InlineKeyboardButton("🔄 Обновить", callback_data="refresh_mygifts")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                gifts_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка: {str(e)}"
            )

    async def auctions_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /auctions command"""
        try:
            # Get current auctions
            auctions_response = await asyncio.to_thread(
                tonnel_api.get_auctions,
                limit=10,
                sort="ending_soon"
            )

            if auctions_response.get("status") == "error":
                await update.message.reply_text(
                    f"❌ Ошибка получения аукционов: {auctions_response.get('error')}"
                )
                return

            auctions = auctions_response.get("data", [])

            if not auctions:
                await update.message.reply_text(
                    "🏆 Активных аукционов не найдено."
                )
                return

            # Format auctions list
            auctions_text = f"🏆 **Активные аукционы ({len(auctions)}):**\n\n"

            for i, auction in enumerate(auctions[:5], 1):  # Show first 5
                name = auction.get("name", "Неизвестно")
                model = auction.get("model", "Неизвестно")
                current_bid = auction.get("auction", {}).get("current_bid", 0)
                end_time = auction.get("auction", {}).get("end_time", "")

                auctions_text += f"""
**{i}. {name}**
🎨 {model}
💰 Текущая ставка: {current_bid} TON
⏰ Завершение: {end_time}
                """

            keyboard = [
                [InlineKeyboardButton("🔄 Обновить", callback_data="refresh_auctions")],
                [InlineKeyboardButton("🎯 Мои ставки", callback_data="my_bids")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                auctions_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка: {str(e)}"
            )

    async def trends_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /trends command"""
        if not context.args:
            await update.message.reply_text(
                "📈 **Анализ трендов**\n\n"
                "Использование: `/trends <название подарка>`\n\n"
                "Примеры:\n"
                "`/trends toy bear`\n"
                "`/trends lunar snake`",
                parse_mode=ParseMode.MARKDOWN
            )
            return

        gift_name = " ".join(context.args)

        try:
            from analytics import market_analytics

            # Get trend analysis
            analysis = await market_analytics.analyze_gift_performance(gift_name, days=7)

            if "error" in analysis:
                await update.message.reply_text(
                    f"❌ Ошибка анализа: {analysis['error']}"
                )
                return

            # Format analysis
            trends_text = f"""
📈 **Анализ трендов: {gift_name}**

💰 **Цены:**
Текущая: {analysis['current_price']} TON
Средняя: {analysis['average_price']} TON
Мин/Макс: {analysis['min_price']} / {analysis['max_price']} TON

📊 **Тренд:**
Направление: {analysis['trend_direction']}
Сила тренда: {analysis['trend_strength']:.2%}
Волатильность: {analysis['volatility']:.2%}

📈 **Изменения:**
24ч: {analysis['price_change_24h']:.1f}%
7д: {analysis['price_change_7d']:.1f}%

🎯 **Уровни:**
Поддержка: {analysis['support_level']} TON
Сопротивление: {analysis['resistance_level']} TON

📊 Данных: {analysis['data_points']} точек за {analysis['analysis_period']} дней
            """

            keyboard = [
                [InlineKeyboardButton("📊 График", callback_data=f"chart_{gift_name}")],
                [InlineKeyboardButton("🔮 Прогноз", callback_data=f"predict_{gift_name}")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                trends_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка анализа: {str(e)}"
            )

    async def watchlist_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /watchlist command"""
        user_id = update.effective_user.id

        try:
            watchlist = await db.get_watchlist(user_id)

            if not watchlist:
                await update.message.reply_text(
                    "⭐ **Твой вотчлист пуст**\n\n"
                    "Добавь подарки для отслеживания:\n"
                    "1. Найди подарок: `/search toy bear`\n"
                    "2. Нажми 'Добавить в вотчлист'\n"
                    "3. Получай уведомления о снижении цен!",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Format watchlist
            watchlist_text = f"⭐ **Твой вотчлист ({len(watchlist)}):**\n\n"

            for i, item in enumerate(watchlist[:10], 1):  # Show first 10
                gift_name = item.get("gift_name", "Неизвестно")
                model = item.get("model", "Любая")
                max_price = item.get("max_price", "Не установлена")
                asset = item.get("asset", "TON")

                watchlist_text += f"""
**{i}. {gift_name}**
🎨 Модель: {model}
🎯 Макс. цена: {max_price} {asset}
                """

            if len(watchlist) > 10:
                watchlist_text += f"\n... и еще {len(watchlist) - 10} позиций"

            keyboard = [
                [InlineKeyboardButton("➕ Добавить", callback_data="watchlist_add")],
                [InlineKeyboardButton("🗑 Очистить", callback_data="watchlist_clear")],
                [InlineKeyboardButton("🔄 Проверить", callback_data="watchlist_check")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                watchlist_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            await update.message.reply_text(
                f"❌ Ошибка: {str(e)}"
            )

    async def run(self):
        """Run the bot"""
        # Initialize database
        await db.init_db()
        
        # Start the bot
        await self.app.initialize()
        await self.app.start()
        await self.app.updater.start_polling()
        
        logger.info("Bot started successfully!")
        
        # Keep running
        try:
            await asyncio.Event().wait()
        except KeyboardInterrupt:
            logger.info("Stopping bot...")
        finally:
            await self.app.updater.stop()
            await self.app.stop()
            await self.app.shutdown()

if __name__ == "__main__":
    bot = TonnelBot()
    asyncio.run(bot.run())
