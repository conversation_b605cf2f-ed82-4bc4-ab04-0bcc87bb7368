# 🚀 Быстрый запуск Tonnel Trading Bot

## ⚡ Мгновенный запуск (1 команда)

```bash
python run.py
```

Этот скрипт автоматически:
- ✅ Проверит Python версию
- ✅ Установит все зависимости  
- ✅ Протестирует бота
- ✅ Запустит бота

## 📋 Пошаговая инструкция

### 1. Установка зависимостей

```bash
pip install -r requirements.txt
```

### 2. Запуск бота

```bash
python main.py
```

### 3. Настройка авторизации

1. Откройте Telegram и найдите вашего бота
2. Отправьте `/start`
3. Получите Auth Data:
   - Зайдите на [market.tonnel.network](https://market.tonnel.network)
   - Войдите в аккаунт
   - Нажмите F12 → Application → Local Storage → web-initData
   - Скопируйте значение
4. В боте: `/auth ВАШИ_ДАННЫЕ`

## 🎯 Первые шаги

### Проверьте баланс
```
/balance
```

### Найдите арбитраж
```
/arbitrage
```

### Поищите подарки
```
/search toy bear
```

### Посмотрите рынок
```
/market
```

## 🔧 Если что-то не работает

### Проблемы с установкой

**Windows:**
```bash
pip install --upgrade pip
pip install curl-cffi --no-cache-dir
pip install python-telegram-bot==20.7
```

**Linux/Mac:**
```bash
pip3 install -r requirements.txt
python3 main.py
```

### Ошибки авторизации

1. Убедитесь, что вы вошли в аккаунт на market.tonnel.network
2. Скопируйте auth data полностью (обычно очень длинная строка)
3. Попробуйте получить новые данные авторизации

### Бот не отвечает

1. Проверьте интернет соединение
2. Убедитесь, что токен бота правильный
3. Проверьте логи в файле `tonnel_bot.log`

## 📊 Основные функции

### 🔍 Поиск и анализ
- `/search <название>` - поиск подарков
- `/arbitrage` - арбитражные возможности  
- `/market` - обзор рынка
- `/trends <название>` - анализ трендов

### 💰 Управление аккаунтом
- `/balance` - баланс и информация
- `/mygifts` - ваши подарки
- `/watchlist` - отслеживание цен

### 🏆 Аукционы
- `/auctions` - активные аукционы

## 🎁 Примеры команд

```bash
# Поиск подарков
/search toy bear
/search lunar snake
/search winter wreath

# Анализ трендов
/trends toy bear
/trends lunar snake

# Проверка арбитража
/arbitrage

# Обзор рынка
/market
```

## 💡 Советы по использованию

### Для арбитража:
1. Запустите `/arbitrage` для поиска возможностей
2. Настройте уведомления для быстрого реагирования
3. Изучите рынок перед крупными покупками

### Для долгосрочных инвестиций:
1. Используйте `/trends` для анализа
2. Добавляйте интересные подарки в вотчлист
3. Отслеживайте портфолио через `/mygifts`

### Для активной торговли:
1. Следите за `/market` обзорами
2. Настройте ценовые алерты
3. Используйте аукционы для выгодных покупок

## 🔔 Настройка уведомлений

Бот автоматически отправляет уведомления о:
- 🚀 Арбитражных возможностях
- ⭐ Срабатывании вотчлиста  
- 📉 Падении цен
- 🆕 Новых выгодных лотах

## 📈 Мониторинг прибыли

1. Регулярно проверяйте `/balance`
2. Анализируйте портфолио через `/mygifts`
3. Отслеживайте тренды ваших подарков
4. Используйте аналитику для принятия решений

## ⚠️ Важные моменты

- **Безопасность**: Никому не передавайте auth data
- **Риски**: Торговля связана с рисками потерь
- **Комиссии**: Учитывайте 10% комиссию Tonnel при покупке
- **Лимиты**: Соблюдайте лимиты API для стабильной работы

## 🆘 Получить помощь

1. Проверьте логи: `tonnel_bot.log`
2. Перезапустите бота: `python main.py`
3. Переустановите зависимости: `pip install -r requirements.txt --force-reinstall`

## 🎯 Цель бота

Максимизировать вашу прибыль от торговли подарками на Tonnel Marketplace через:
- Автоматический поиск арбитражных возможностей
- Умные уведомления о выгодных сделках
- Глубокую аналитику рынка
- Удобные инструменты управления портфолио

---

**🚀 Готово! Теперь вы можете зарабатывать на торговле подарками!**

Запустите бота и начните получать прибыль уже сегодня! 💰
