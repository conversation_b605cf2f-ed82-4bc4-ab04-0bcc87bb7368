#!/usr/bin/env python3
"""
Simplified Tonnel Trading Bot - Working Version
"""

import asyncio
import logging
import json
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes
from telegram.constants import ParseMode
from curl_cffi import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Bot configuration
BOT_TOKEN = "8154816134:AAHsoim6iOysQ6_VJQI0plUE0S9Mx4jewR4"

class SimpleTonnelBot:
    def __init__(self):
        self.app = Application.builder().token(BOT_TOKEN).build()
        self.setup_handlers()
        self.user_auth_data = {}  # Simple in-memory storage

    def setup_handlers(self):
        """Setup bot handlers"""
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("help", self.help_command))
        self.app.add_handler(<PERSON><PERSON>andler("auth", self.auth_command))
        self.app.add_handler(CommandHandler("search", self.search_command))
        self.app.add_handler(CommandHandler("arbitrage", self.arbitrage_command))
        self.app.add_handler(CommandHandler("balance", self.balance_command))
        self.app.add_handler(CallbackQueryHandler(self.button_callback))

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_text = """
🎁 **Tonnel Trading Bot**

Привет! Я помогу тебе торговать на Tonnel Marketplace.

**Команды:**
/help - Справка
/auth - Настройка авторизации
/search <название> - Поиск подарков
/arbitrage - Поиск арбитража
/balance - Проверка баланса

**Для начала:**
1. Настрой авторизацию: /auth
2. Найди подарки: /search toy bear
3. Найди арбитраж: /arbitrage
        """
        
        keyboard = [
            [InlineKeyboardButton("🔍 Поиск подарков", callback_data="search_demo")],
            [InlineKeyboardButton("💎 Арбитраж", callback_data="arbitrage_demo")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(welcome_text, parse_mode=ParseMode.MARKDOWN, reply_markup=reply_markup)

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_text = """
🤖 **Команды бота:**

/start - Запуск бота
/help - Справка
/auth <данные> - Настройка авторизации
/search <название> - Поиск подарков
/arbitrage - Поиск арбитража
/balance - Проверка баланса

**Примеры:**
`/search toy bear` - найти Toy Bear
`/auth YOUR_AUTH_DATA` - настроить авторизацию

**Получение Auth Data:**
1. Зайди на market.tonnel.network
2. Войди в аккаунт
3. F12 → Application → Local Storage → web-initData
4. Скопируй значение
        """
        await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)

    async def auth_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /auth command"""
        user_id = update.effective_user.id
        
        if not context.args:
            await update.message.reply_text(
                "🔑 **Настройка авторизации**\n\n"
                "Отправь: `/auth ТВОИ_ДАННЫЕ`\n\n"
                "Как получить данные:\n"
                "1. market.tonnel.network\n"
                "2. F12 → Application → Local Storage → web-initData\n"
                "3. Скопируй значение",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        auth_data = " ".join(context.args)
        
        # Test auth data
        if await self.test_auth_data(auth_data):
            self.user_auth_data[user_id] = auth_data
            await update.message.reply_text("✅ Авторизация успешна!")
        else:
            await update.message.reply_text(
                "❌ Неверные данные авторизации.\n"
                "Получи новые данные с market.tonnel.network"
            )

    async def search_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /search command"""
        if not context.args:
            await update.message.reply_text(
                "🔍 Использование: `/search <название>`\n"
                "Пример: `/search toy bear`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        gift_name = " ".join(context.args)
        await update.message.reply_text(f"🔍 Ищу подарки: {gift_name}...")
        
        gifts = await self.get_gifts(gift_name)
        
        if not gifts:
            await update.message.reply_text(f"❌ Подарки '{gift_name}' не найдены.")
            return
        
        # Format results
        results_text = f"🎁 **Найдено: {len(gifts)} подарков**\n\n"
        
        for i, gift in enumerate(gifts[:5], 1):
            price = gift.get("price", 0)
            model = gift.get("model", "Unknown")
            
            results_text += f"""
**{i}. {gift.get('name', 'Unknown')}**
💰 {price} {gift.get('asset', 'TON')}
🎨 {model}
🔢 #{gift.get('gift_num', 'N/A')}
            """
        
        await update.message.reply_text(results_text, parse_mode=ParseMode.MARKDOWN)

    async def arbitrage_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /arbitrage command"""
        await update.message.reply_text("🔍 Ищу арбитражные возможности...")
        
        opportunities = await self.find_arbitrage()
        
        if not opportunities:
            await update.message.reply_text("📊 Арбитражных возможностей не найдено.")
            return
        
        results_text = f"💎 **Найдено возможностей: {len(opportunities)}**\n\n"
        
        for i, opp in enumerate(opportunities[:3], 1):
            profit_margin = opp["profit_margin"] * 100
            
            results_text += f"""
**{i}. {opp['name']}**
💰 Цена: {opp['price']} TON
🚀 Прибыль: {profit_margin:.1f}%
📊 Потенциал: {opp['potential_profit']:.2f} TON
            """
        
        await update.message.reply_text(results_text, parse_mode=ParseMode.MARKDOWN)

    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /balance command"""
        user_id = update.effective_user.id
        
        if user_id not in self.user_auth_data:
            await update.message.reply_text("❌ Сначала настрой авторизацию: /auth")
            return
        
        auth_data = self.user_auth_data[user_id]
        user_info = await self.get_user_info(auth_data)
        
        if not user_info or user_info.get("status") == "error":
            await update.message.reply_text("❌ Ошибка получения баланса. Обнови авторизацию: /auth")
            return
        
        balance_text = f"""
💰 **Твой баланс:**

🪙 TON: {user_info.get('balance', 0):.6f}
💎 USDT: {user_info.get('usdtBalance', 0):.6f}
🎯 TONNEL: {user_info.get('tonnelBalance', 0):.6f}

👤 **Профиль:**
Имя: {user_info.get('name', 'Unknown')}
        """
        
        await update.message.reply_text(balance_text, parse_mode=ParseMode.MARKDOWN)

    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()
        
        if query.data == "search_demo":
            await query.edit_message_text("🔍 Демо поиска...")
            gifts = await self.get_gifts("toy bear")
            
            if gifts:
                text = f"🎁 Найдено {len(gifts)} подарков Toy Bear\n"
                text += f"Самый дешевый: {gifts[0].get('price', 0)} TON"
            else:
                text = "❌ Подарки не найдены"
            
            await query.edit_message_text(text)
        
        elif query.data == "arbitrage_demo":
            await query.edit_message_text("💎 Демо арбитража...")
            opportunities = await self.find_arbitrage()
            
            if opportunities:
                opp = opportunities[0]
                text = f"🚀 Лучшая возможность:\n{opp['name']} - {opp['profit_margin']*100:.1f}% прибыли"
            else:
                text = "📊 Возможностей не найдено"
            
            await query.edit_message_text(text)

    async def get_gifts(self, gift_name=None, limit=10):
        """Get gifts from Tonnel API"""
        try:
            filter_data = {
                "price": {"$exists": True},
                "refunded": {"$ne": True},
                "buyer": {"$exists": False},
                "export_at": {"$exists": True},
                "asset": "TON"
            }
            
            if gift_name:
                filter_data["gift_name"] = gift_name
            
            json_data = {
                'page': 1,
                'limit': limit,
                'sort': '{"price":1}',
                'filter': json.dumps(filter_data),
                'price_range': None,
                'user_auth': '',
            }
            
            headers = {
                'origin': 'https://market.tonnel.network',
                'referer': 'https://market.tonnel.network/',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.post(
                'https://gifts2.tonnel.network/api/pageGifts',
                json=json_data,
                headers=headers,
                impersonate="chrome",
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data if isinstance(data, list) else []
            
        except Exception as e:
            logger.error(f"Error getting gifts: {e}")
        
        return []

    async def find_arbitrage(self):
        """Find arbitrage opportunities"""
        try:
            gifts = await self.get_gifts(limit=50)
            
            if not gifts:
                return []
            
            # Group by name
            gift_groups = {}
            for gift in gifts:
                name = gift.get('name', '')
                if name not in gift_groups:
                    gift_groups[name] = []
                gift_groups[name].append(gift)
            
            opportunities = []
            
            for name, group in gift_groups.items():
                if len(group) < 2:
                    continue
                
                # Sort by price
                group.sort(key=lambda x: x.get('price', 0))
                
                lowest_price = group[0].get('price', 0)
                avg_price = sum(g.get('price', 0) for g in group[1:4]) / min(3, len(group)-1)
                
                if avg_price > lowest_price:
                    profit_margin = (avg_price - lowest_price) / lowest_price
                    
                    if profit_margin > 0.1:  # 10% minimum profit
                        opportunities.append({
                            'name': name,
                            'price': lowest_price,
                            'avg_price': avg_price,
                            'profit_margin': profit_margin,
                            'potential_profit': avg_price - lowest_price
                        })
            
            # Sort by profit margin
            opportunities.sort(key=lambda x: x['profit_margin'], reverse=True)
            return opportunities[:10]
            
        except Exception as e:
            logger.error(f"Error finding arbitrage: {e}")
            return []

    async def test_auth_data(self, auth_data):
        """Test if auth data is valid"""
        try:
            user_info = await self.get_user_info(auth_data)
            return user_info and user_info.get("status") != "error"
        except:
            return False

    async def get_user_info(self, auth_data):
        """Get user info from Tonnel API"""
        try:
            json_data = {'user_auth': auth_data}
            
            headers = {
                'origin': 'https://market.tonnel.network',
                'referer': 'https://market.tonnel.network/',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.post(
                'https://gifts2.tonnel.network/api/userInfo',
                json=json_data,
                headers=headers,
                impersonate="chrome",
                timeout=30
            )
            
            if response.status_code == 200:
                return response.json()
            
        except Exception as e:
            logger.error(f"Error getting user info: {e}")
        
        return None

    async def run(self):
        """Run the bot"""
        logger.info("Starting Simple Tonnel Bot...")
        
        await self.app.initialize()
        await self.app.start()
        await self.app.updater.start_polling()
        
        logger.info("✅ Bot started successfully!")
        
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Stopping bot...")
        finally:
            await self.app.updater.stop()
            await self.app.stop()
            await self.app.shutdown()

if __name__ == "__main__":
    bot = SimpleTonnelBot()
    asyncio.run(bot.run())
