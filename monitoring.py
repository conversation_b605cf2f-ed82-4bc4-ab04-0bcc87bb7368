import asyncio
import logging
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict
from telegram import <PERSON><PERSON>
from telegram.constants import ParseMode

import config
from database import db
from arbitrage import arbitrage_analyzer
from tonnel_api import tonnel_api

logger = logging.getLogger(__name__)

class MarketMonitor:
    def __init__(self, bot: Bot):
        self.bot = bot
        self.is_running = False
        self.last_notifications = {}

    async def start_monitoring(self):
        """Start market monitoring"""
        self.is_running = True
        logger.info("Market monitoring started")
        
        while self.is_running:
            try:
                await self.check_arbitrage_opportunities()
                await self.check_watchlist_alerts()
                await self.record_market_data()
                
                # Wait before next check
                await asyncio.sleep(config.UPDATE_INTERVAL)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def stop_monitoring(self):
        """Stop market monitoring"""
        self.is_running = False
        logger.info("Market monitoring stopped")

    async def check_arbitrage_opportunities(self):
        """Check for new arbitrage opportunities and notify users"""
        try:
            opportunities = await arbitrage_analyzer.find_arbitrage_opportunities(
                threshold=config.ARBITRAGE_THRESHOLD
            )
            
            if not opportunities:
                return
            
            # Get users who want arbitrage notifications
            users = await self.get_notification_users()
            
            for user_id in users:
                try:
                    user_data = await db.get_user(user_id)
                    if not user_data or not user_data.get("notifications_enabled"):
                        continue
                    
                    threshold = user_data.get("arbitrage_threshold", config.ARBITRAGE_THRESHOLD)
                    
                    # Filter opportunities by user's threshold
                    user_opportunities = [
                        opp for opp in opportunities 
                        if opp["profit_margin"] >= threshold
                    ]
                    
                    if user_opportunities:
                        await self.send_arbitrage_notification(user_id, user_opportunities[:3])
                        
                except Exception as e:
                    logger.error(f"Error sending arbitrage notification to {user_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Error checking arbitrage opportunities: {e}")

    async def check_watchlist_alerts(self):
        """Check watchlist items for price alerts"""
        try:
            # Get all users with watchlist items
            users = await self.get_watchlist_users()
            
            for user_id in users:
                try:
                    user_data = await db.get_user(user_id)
                    if not user_data or not user_data.get("notifications_enabled"):
                        continue
                    
                    auth_data = user_data.get("auth_data", "")
                    alerts = await arbitrage_analyzer.check_watchlist_alerts(user_id, auth_data)
                    
                    if alerts:
                        await self.send_watchlist_alerts(user_id, alerts)
                        
                except Exception as e:
                    logger.error(f"Error checking watchlist for {user_id}: {e}")
                    
        except Exception as e:
            logger.error(f"Error checking watchlist alerts: {e}")

    async def record_market_data(self):
        """Record current market data for analytics"""
        try:
            # Get current market data
            market_data = await arbitrage_analyzer.get_market_data(limit=50)
            
            for gift in market_data:
                await db.record_price(
                    gift_name=gift.get("name", ""),
                    model=gift.get("model", ""),
                    backdrop=gift.get("backdrop", ""),
                    symbol=gift.get("symbol", ""),
                    price=gift.get("price", 0),
                    asset=gift.get("asset", "TON")
                )
                
        except Exception as e:
            logger.error(f"Error recording market data: {e}")

    async def send_arbitrage_notification(self, user_id: int, opportunities: List[Dict]):
        """Send arbitrage opportunity notification"""
        try:
            # Check notification cooldown
            cooldown_key = f"arbitrage_{user_id}"
            last_notification = self.last_notifications.get(cooldown_key)
            
            if last_notification and \
               datetime.now() - last_notification < timedelta(minutes=5):
                return
            
            message = "🚀 **Новые арбитражные возможности!**\n\n"
            
            for i, opp in enumerate(opportunities, 1):
                gift = opp["gift"]
                profit_margin = opp["profit_margin"] * 100
                potential_profit = opp["potential_profit"]
                
                message += f"""
**{i}. {gift.get('name', 'Неизвестно')}**
🎨 {gift.get('model', 'Неизвестно')}
💰 Цена: {opp['lowest_price']} TON
🚀 Прибыль: {profit_margin:.1f}% ({potential_profit:.2f} TON)
🔢 ID: {gift.get('gift_id')}
                """
            
            message += f"\n⏰ {datetime.now().strftime('%H:%M:%S')}"
            
            await self.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode=ParseMode.MARKDOWN
            )
            
            # Update notification time
            self.last_notifications[cooldown_key] = datetime.now()
            
            # Record notification
            for opp in opportunities:
                await db.add_notification(
                    user_id=user_id,
                    gift_id=opp["gift"].get("gift_id", 0),
                    gift_name=opp["gift"].get("name", ""),
                    price=opp["lowest_price"],
                    notification_type="arbitrage"
                )
                
        except Exception as e:
            logger.error(f"Error sending arbitrage notification: {e}")

    async def send_watchlist_alerts(self, user_id: int, alerts: List[Dict]):
        """Send watchlist price alerts"""
        try:
            message = "⭐ **Срабатывание вотчлиста!**\n\n"
            
            for alert in alerts[:3]:  # Limit to 3 alerts per message
                gift = alert["gift"]
                target_price = alert["target_price"]
                current_price = alert["current_price"]
                savings = alert["savings"]
                
                message += f"""
**{gift.get('name', 'Неизвестно')}**
🎨 {gift.get('model', 'Неизвестно')}
🎯 Целевая цена: {target_price} TON
💰 Текущая цена: {current_price} TON
💸 Экономия: {savings:.2f} TON
🔢 ID: {gift.get('gift_id')}
                """
            
            message += f"\n⏰ {datetime.now().strftime('%H:%M:%S')}"
            
            await self.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode=ParseMode.MARKDOWN
            )
            
            # Record notifications
            for alert in alerts:
                await db.add_notification(
                    user_id=user_id,
                    gift_id=alert["gift"].get("gift_id", 0),
                    gift_name=alert["gift"].get("name", ""),
                    price=alert["current_price"],
                    notification_type="watchlist"
                )
                
        except Exception as e:
            logger.error(f"Error sending watchlist alerts: {e}")

    async def get_notification_users(self) -> List[int]:
        """Get list of users who want notifications"""
        try:
            # This would be implemented with a proper database query
            # For now, return empty list as placeholder
            return []
        except Exception as e:
            logger.error(f"Error getting notification users: {e}")
            return []

    async def get_watchlist_users(self) -> List[int]:
        """Get list of users with watchlist items"""
        try:
            # This would be implemented with a proper database query
            # For now, return empty list as placeholder
            return []
        except Exception as e:
            logger.error(f"Error getting watchlist users: {e}")
            return []

    async def send_market_summary(self, user_id: int):
        """Send daily market summary"""
        try:
            user_data = await db.get_user(user_id)
            auth_data = user_data.get("auth_data", "") if user_data else ""
            
            summary = await arbitrage_analyzer.get_market_summary(auth_data)
            
            if "error" in summary:
                return
            
            message = f"""
📊 **Ежедневный обзор рынка**

📈 **Статистика за сегодня:**
Всего лотов: {summary['total_listings']}
Средняя цена: {summary['average_price']} TON
Медианная цена: {summary['median_price']} TON

💎 **Арбитраж:**
Найдено возможностей: {summary['arbitrage_opportunities']}
            """
            
            if summary.get('top_opportunity'):
                top = summary['top_opportunity']
                profit = top['profit_margin'] * 100
                message += f"""
🚀 **Лучшая возможность:**
{top['gift']['name']} - {profit:.1f}% прибыли
                """
            
            message += f"\n📅 {datetime.now().strftime('%d.%m.%Y')}"
            
            await self.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error sending market summary: {e}")

class NotificationManager:
    def __init__(self, bot: Bot):
        self.bot = bot

    async def notify_price_drop(self, user_id: int, gift_data: Dict, old_price: float, new_price: float):
        """Notify about significant price drop"""
        try:
            drop_percentage = ((old_price - new_price) / old_price) * 100
            
            if drop_percentage < 10:  # Only notify for 10%+ drops
                return
            
            message = f"""
📉 **Значительное падение цены!**

🎁 **{gift_data.get('name', 'Неизвестно')}**
🎨 {gift_data.get('model', 'Неизвестно')}
📊 Было: {old_price} TON
💰 Стало: {new_price} TON
📉 Падение: {drop_percentage:.1f}%

🔢 ID: {gift_data.get('gift_id')}
⏰ {datetime.now().strftime('%H:%M:%S')}
            """
            
            await self.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error sending price drop notification: {e}")

    async def notify_new_listing(self, user_id: int, gift_data: Dict):
        """Notify about new listing matching criteria"""
        try:
            message = f"""
🆕 **Новый лот по твоим критериям!**

🎁 **{gift_data.get('name', 'Неизвестно')}**
🎨 {gift_data.get('model', 'Неизвестно')}
💰 Цена: {gift_data.get('price', 0)} TON
🔢 ID: {gift_data.get('gift_id')}

⏰ {datetime.now().strftime('%H:%M:%S')}
            """
            
            await self.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error sending new listing notification: {e}")

# Global instances will be initialized in main bot
market_monitor = None
notification_manager = None
