#!/usr/bin/env python3
"""
Tonnel Trading Bot - Main Entry Point
Advanced Telegram bot for Tonnel Marketplace trading and analytics
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime

from telegram import Bo<PERSON>
from bot import TonnelBot
from monitoring import MarketMonitor, NotificationManager
import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tonnel_bot.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class TonnelBotManager:
    def __init__(self):
        self.bot_instance = None
        self.market_monitor = None
        self.notification_manager = None
        self.telegram_bot = None
        self.running = False

    async def initialize(self):
        """Initialize all bot components"""
        try:
            logger.info("Initializing Tonnel Trading Bot...")
            
            # Initialize Telegram bot
            self.telegram_bot = Bot(token=config.BOT_TOKEN)
            
            # Initialize main bot
            self.bot_instance = TonnelBot()
            
            # Initialize monitoring components
            self.market_monitor = MarketMonitor(self.telegram_bot)
            self.notification_manager = NotificationManager(self.telegram_bot)
            
            logger.info("Bot components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize bot: {e}")
            raise

    async def start(self):
        """Start the bot and all monitoring services"""
        try:
            logger.info("Starting Tonnel Trading Bot...")
            
            # Start the main bot
            bot_task = asyncio.create_task(self.bot_instance.run())
            
            # Start market monitoring
            monitor_task = asyncio.create_task(self.market_monitor.start_monitoring())
            
            self.running = True
            logger.info("🚀 Tonnel Trading Bot started successfully!")
            logger.info(f"Bot token: {config.BOT_TOKEN[:20]}...")
            logger.info(f"Monitoring interval: {config.UPDATE_INTERVAL} seconds")
            logger.info(f"Database: {config.DATABASE_PATH}")
            
            # Wait for tasks to complete
            await asyncio.gather(bot_task, monitor_task)
            
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            await self.stop()
            raise

    async def stop(self):
        """Stop the bot and all services"""
        try:
            logger.info("Stopping Tonnel Trading Bot...")
            
            self.running = False
            
            # Stop monitoring
            if self.market_monitor:
                await self.market_monitor.stop_monitoring()
            
            # Stop main bot
            if self.bot_instance:
                # The bot's stop method will be called automatically
                pass
            
            logger.info("Bot stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping bot: {e}")

    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """Main entry point"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    TONNEL TRADING BOT                        ║
    ║                                                              ║
    ║  🎁 Advanced Telegram bot for Tonnel Marketplace trading    ║
    ║  🔍 Real-time arbitrage detection                           ║
    ║  📊 Market analytics and price tracking                     ║
    ║  ⚡ Automated notifications and alerts                      ║
    ║  💰 Portfolio management and trading tools                  ║
    ║                                                              ║
    ║  Created for maximum profit with minimal effort             ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    bot_manager = TonnelBotManager()
    
    try:
        # Setup signal handlers
        bot_manager.setup_signal_handlers()
        
        # Initialize and start
        await bot_manager.initialize()
        await bot_manager.start()
        
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
    finally:
        await bot_manager.stop()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
