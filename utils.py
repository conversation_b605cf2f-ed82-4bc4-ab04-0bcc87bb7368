"""
Utility functions for Tonnel Trading Bot
"""

import re
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any
import hashlib
import asyncio
from functools import wraps

def format_price(price: Union[int, float], asset: str = "TON") -> str:
    """Format price with proper decimals and asset symbol"""
    if price == 0:
        return f"0 {asset}"
    
    if price < 0.001:
        return f"{price:.6f} {asset}"
    elif price < 1:
        return f"{price:.4f} {asset}"
    elif price < 100:
        return f"{price:.2f} {asset}"
    else:
        return f"{price:.1f} {asset}"

def format_percentage(value: float, decimals: int = 1) -> str:
    """Format percentage with proper sign"""
    if value > 0:
        return f"+{value:.{decimals}f}%"
    elif value < 0:
        return f"{value:.{decimals}f}%"
    else:
        return "0%"

def format_time_ago(timestamp: Union[str, datetime]) -> str:
    """Format time as 'X ago' string"""
    if isinstance(timestamp, str):
        timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
    
    now = datetime.now(timestamp.tzinfo) if timestamp.tzinfo else datetime.now()
    diff = now - timestamp
    
    if diff.days > 0:
        return f"{diff.days}д назад"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours}ч назад"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes}м назад"
    else:
        return "только что"

def validate_auth_data(auth_data: str) -> bool:
    """Validate Tonnel auth data format"""
    if not auth_data or len(auth_data) < 50:
        return False
    
    # Basic validation - should contain typical Telegram initData format
    required_fields = ['user=', 'auth_date=', 'hash=']
    return all(field in auth_data for field in required_fields)

def extract_gift_info(gift_data: Dict) -> Dict:
    """Extract and format gift information"""
    return {
        "id": gift_data.get("gift_id", 0),
        "name": gift_data.get("name", "Unknown"),
        "model": gift_data.get("model", "Unknown"),
        "backdrop": gift_data.get("backdrop", "Unknown"),
        "symbol": gift_data.get("symbol", "Unknown"),
        "price": gift_data.get("price", 0),
        "asset": gift_data.get("asset", "TON"),
        "gift_num": gift_data.get("gift_num", 0),
        "status": gift_data.get("status", "unknown")
    }

def calculate_profit_margin(buy_price: float, sell_price: float, fee_rate: float = 0.1) -> Dict:
    """Calculate profit margin considering fees"""
    if buy_price <= 0:
        return {"error": "Invalid buy price"}
    
    # Tonnel adds 10% fee on purchase
    total_cost = buy_price * (1 + fee_rate)
    
    if sell_price <= total_cost:
        profit = sell_price - total_cost
        margin = (profit / total_cost) * 100
    else:
        profit = sell_price - total_cost
        margin = (profit / total_cost) * 100
    
    return {
        "buy_price": buy_price,
        "total_cost": total_cost,
        "sell_price": sell_price,
        "profit": profit,
        "margin_percent": margin,
        "profitable": profit > 0
    }

def parse_search_query(query: str) -> Dict:
    """Parse search query into components"""
    # Remove extra spaces and convert to lowercase
    query = re.sub(r'\s+', ' ', query.strip().lower())
    
    # Try to extract specific components
    result = {
        "gift_name": query,
        "model": None,
        "backdrop": None,
        "symbol": None,
        "max_price": None
    }
    
    # Look for price filter
    price_match = re.search(r'price\s*[<>=]\s*(\d+(?:\.\d+)?)', query)
    if price_match:
        result["max_price"] = float(price_match.group(1))
        query = re.sub(r'price\s*[<>=]\s*\d+(?:\.\d+)?', '', query).strip()
    
    # Look for model specification
    model_match = re.search(r'model\s*[:=]\s*([^,]+)', query)
    if model_match:
        result["model"] = model_match.group(1).strip()
        query = re.sub(r'model\s*[:=]\s*[^,]+', '', query).strip()
    
    # Clean up gift name
    result["gift_name"] = re.sub(r'[,;]', '', query).strip()
    
    return result

def generate_cache_key(*args) -> str:
    """Generate cache key from arguments"""
    key_string = "_".join(str(arg) for arg in args)
    return hashlib.md5(key_string.encode()).hexdigest()

def safe_divide(numerator: float, denominator: float, default: float = 0) -> float:
    """Safe division with default value"""
    try:
        return numerator / denominator if denominator != 0 else default
    except (TypeError, ZeroDivisionError):
        return default

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def format_large_number(number: Union[int, float]) -> str:
    """Format large numbers with K, M, B suffixes"""
    if number < 1000:
        return str(number)
    elif number < 1000000:
        return f"{number/1000:.1f}K"
    elif number < 1000000000:
        return f"{number/1000000:.1f}M"
    else:
        return f"{number/1000000000:.1f}B"

def validate_ton_address(address: str) -> bool:
    """Basic TON address validation"""
    if not address:
        return False
    
    # TON addresses are typically 48 characters long and contain specific patterns
    # This is a simplified validation
    if len(address) != 48:
        return False
    
    # Should contain only valid base64url characters
    valid_chars = set('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_')
    return all(c in valid_chars for c in address)

def rate_limit(calls_per_minute: int = 30):
    """Rate limiting decorator"""
    def decorator(func):
        calls = []
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            now = datetime.now()
            # Remove calls older than 1 minute
            calls[:] = [call_time for call_time in calls if now - call_time < timedelta(minutes=1)]
            
            if len(calls) >= calls_per_minute:
                # Calculate wait time
                wait_time = 60 - (now - calls[0]).total_seconds()
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
                    # Remove the oldest call
                    calls.pop(0)
            
            calls.append(now)
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator

def chunk_list(lst: List, chunk_size: int) -> List[List]:
    """Split list into chunks of specified size"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

def merge_dicts(*dicts: Dict) -> Dict:
    """Merge multiple dictionaries"""
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result

def extract_numbers(text: str) -> List[float]:
    """Extract all numbers from text"""
    pattern = r'-?\d+(?:\.\d+)?'
    matches = re.findall(pattern, text)
    return [float(match) for match in matches]

def format_duration(seconds: int) -> str:
    """Format duration in human readable format"""
    if seconds < 60:
        return f"{seconds}с"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes}м"
    elif seconds < 86400:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}ч {minutes}м"
    else:
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        return f"{days}д {hours}ч"

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations"""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Limit length
    if len(filename) > 255:
        filename = filename[:255]
    
    return filename

def is_valid_gift_name(name: str) -> bool:
    """Validate gift name format"""
    if not name or len(name) < 2:
        return False
    
    # Should not contain only numbers or special characters
    if name.isdigit() or not any(c.isalpha() for c in name):
        return False
    
    return True

def calculate_rarity_score(model: str, backdrop: str, symbol: str) -> float:
    """Calculate rarity score based on percentages in attributes"""
    def extract_percentage(attr: str) -> float:
        match = re.search(r'\((\d+(?:\.\d+)?)%\)', attr)
        return float(match.group(1)) if match else 100.0
    
    model_rarity = extract_percentage(model)
    backdrop_rarity = extract_percentage(backdrop)
    symbol_rarity = extract_percentage(symbol)
    
    # Lower percentage = higher rarity = higher score
    # Use inverse and normalize
    score = (1/model_rarity + 1/backdrop_rarity + 1/symbol_rarity) * 100
    return round(score, 2)

class AsyncCache:
    """Simple async cache implementation"""
    def __init__(self, ttl: int = 300):  # 5 minutes default TTL
        self.cache = {}
        self.ttl = ttl
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if key in self.cache:
            value, timestamp = self.cache[key]
            if datetime.now() - timestamp < timedelta(seconds=self.ttl):
                return value
            else:
                del self.cache[key]
        return None
    
    async def set(self, key: str, value: Any) -> None:
        """Set value in cache"""
        self.cache[key] = (value, datetime.now())
    
    async def clear(self) -> None:
        """Clear all cache"""
        self.cache.clear()
    
    async def delete(self, key: str) -> None:
        """Delete specific key from cache"""
        if key in self.cache:
            del self.cache[key]

# Global cache instance
cache = AsyncCache()
