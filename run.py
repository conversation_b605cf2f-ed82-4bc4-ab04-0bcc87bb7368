#!/usr/bin/env python3
"""
Quick start script for Tonnel Trading Bot
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required. Current version:", sys.version)
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    
    requirements = [
        "python-telegram-bot==20.7",
        "curl-cffi==0.6.2", 
        "aiofiles==23.2.0",
        "aiosqlite==0.19.0",
        "python-dotenv==1.0.0",
        "numpy==1.24.3",
        "pandas==2.0.3",
        "matplotlib==3.7.2",
        "seaborn==0.12.2",
        "Pillow==10.0.0"
    ]
    
    for package in requirements:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    print("✅ All requirements installed successfully!")
    return True

def check_files():
    """Check if all required files exist"""
    required_files = [
        "main.py",
        "bot.py", 
        "tonnel_api.py",
        "database.py",
        "arbitrage.py",
        "monitoring.py",
        "analytics.py",
        "config.py",
        "utils.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required files found")
    return True

def create_env_file():
    """Create .env file if it doesn't exist"""
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 Creating .env file...")
        with open(env_file, "w") as f:
            f.write("# Tonnel Trading Bot Environment Variables\n")
            f.write("# Add your custom settings here\n")
            f.write("\n")
            f.write("# Example:\n")
            f.write("# DEBUG=True\n")
            f.write("# LOG_LEVEL=INFO\n")
        print("✅ .env file created")

async def test_bot():
    """Test bot initialization"""
    print("🧪 Testing bot initialization...")
    
    try:
        # Import main modules to check for errors
        import config
        from database import db
        from tonnel_api import tonnel_api
        
        # Test database initialization
        await db.init_db()
        print("✅ Database initialized successfully")
        
        # Test API module
        print("✅ API module loaded successfully")
        
        print("✅ Bot test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Bot test failed: {e}")
        return False

def main():
    """Main setup and run function"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    TONNEL TRADING BOT                        ║
    ║                      QUICK START                             ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Check Python version
    if not check_python_version():
        return
    
    # Check required files
    if not check_files():
        print("\n❌ Setup failed: Missing required files")
        return
    
    # Install requirements
    install_choice = input("\n📦 Install/update requirements? (y/n): ").lower().strip()
    if install_choice in ['y', 'yes', '']:
        if not install_requirements():
            print("\n❌ Setup failed: Could not install requirements")
            return
    
    # Create env file
    create_env_file()
    
    # Test bot
    test_choice = input("\n🧪 Run bot test? (y/n): ").lower().strip()
    if test_choice in ['y', 'yes', '']:
        try:
            asyncio.run(test_bot())
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return
    
    # Ask to start bot
    start_choice = input("\n🚀 Start the bot now? (y/n): ").lower().strip()
    if start_choice in ['y', 'yes', '']:
        print("\n🚀 Starting Tonnel Trading Bot...")
        print("Press Ctrl+C to stop the bot\n")
        
        try:
            # Import and run main
            from main import main as bot_main
            asyncio.run(bot_main())
        except KeyboardInterrupt:
            print("\n👋 Bot stopped by user")
        except Exception as e:
            print(f"\n❌ Error starting bot: {e}")
    else:
        print("\n✅ Setup completed! Run 'python main.py' to start the bot.")
        print("\nQuick commands:")
        print("  python main.py  - Start the bot")
        print("  python run.py   - Run this setup again")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Setup cancelled by user")
    except Exception as e:
        print(f"\n❌ Setup error: {e}")
        sys.exit(1)
