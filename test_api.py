#!/usr/bin/env python3
"""
Test script for Tonnel API
"""

import json
from curl_cffi import requests

def test_tonnel_api():
    """Test basic Tonnel API functionality"""
    
    # Test auth data (your real data)
    auth_data = "user=%7B%22id%22%3A7125266008%2C%22first_name%22%3A%22psycho%20ceo%22%2C%22last_name%22%3A%22sexuoys%22%2C%22language_code%22%3A%22ru%22%2C%22allows_write_to_pm%22%3Atrue%2C%22photo_url%22%3A%22https%3A%5C%2F%5C%2Ft.me%5C%2Fi%5C%2Fuserpic%5C%2F320%5C%2FhYrT8vOaBsoBuzv2Yle_YtoVO1YtqsPTuf7_gdZaRDrPTXbgicHwvDTZVpiu2Yey%2F%22%7D&chat_instance=8341848793293988634&chat_type=sender&auth_date=1748977858&signature=uD6SjsnFQKVCHqpswHc4bdF1UElGl3BYod-HYqtAloV-juN6n2nEBX2pqPOWoh6TjRp9pdOJMf5-XTsZfpAQ&hash=4e97e19b991e5a2ce6968Sadcbbaf9dae9c5b8043f95504df2e13af607e89c7b"
    
    headers = {
        'origin': 'https://market.tonnel.network',
        'referer': 'https://market.tonnel.network/',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        'content-type': 'application/json'
    }
    
    print("🧪 Testing Tonnel API...")
    
    # Test 1: Get gifts
    print("\n1. Testing pageGifts endpoint...")
    try:
        gifts_data = {
            'page': 1,
            'limit': 5,
            'sort': '{"price":1}',
            'filter': '{"price":{"$exists":true},"refunded":{"$ne":true},"buyer":{"$exists":false},"export_at":{"$exists":true},"asset":"TON"}',
            'price_range': None,
            'user_auth': '',
        }
        
        response = requests.post(
            'https://gifts2.tonnel.network/api/pageGifts',
            json=gifts_data,
            headers=headers,
            impersonate="chrome",
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response type: {type(data)}")
            if isinstance(data, list):
                print(f"Found {len(data)} gifts")
                if data:
                    print(f"First gift: {data[0].get('name', 'Unknown')} - {data[0].get('price', 0)} TON")
            else:
                print(f"Response keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")
    
    # Test 2: User info (with auth)
    print("\n2. Testing userInfo endpoint...")
    try:
        user_data = {'user_auth': auth_data}
        
        response = requests.post(
            'https://gifts2.tonnel.network/api/userInfo',
            json=user_data,
            headers=headers,
            impersonate="chrome",
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")
    
    # Test 3: Alternative info endpoint
    print("\n3. Testing info endpoint...")
    try:
        user_data = {'user_auth': auth_data}
        
        response = requests.post(
            'https://gifts2.tonnel.network/api/info',
            json=user_data,
            headers=headers,
            impersonate="chrome",
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")
    
    # Test 4: Filter stats
    print("\n4. Testing filterStats endpoint...")
    try:
        stats_data = {'user_auth': auth_data}
        
        response = requests.post(
            'https://gifts2.tonnel.network/api/filterStats',
            json=stats_data,
            headers=headers,
            impersonate="chrome",
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response type: {type(data)}")
            if isinstance(data, dict):
                print(f"Keys: {list(data.keys())}")
                if 'data' in data:
                    print(f"Data items: {len(data['data'])}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_tonnel_api()
