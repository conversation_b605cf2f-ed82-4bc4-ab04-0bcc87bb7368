import asyncio
import json
from typing import Dict, <PERSON>, Tuple, Optional
from datetime import datetime, timedelta
import statistics
from tonnel_api import tonnel_api
from database import db
import config

class ArbitrageAnalyzer:
    def __init__(self):
        self.price_cache = {}
        self.last_update = {}
        self.notification_cooldowns = {}

    async def get_market_data(self, gift_name: str = None, limit: int = 100) -> List[Dict]:
        """Get current market data"""
        try:
            response = await asyncio.to_thread(
                tonnel_api.get_gifts,
                gift_name=gift_name,
                limit=limit,
                sort="price_asc"
            )
            
            if response.get("status") == "error":
                print(f"Error getting market data: {response.get('error')}")
                return []
                
            return response.get("data", [])
        except Exception as e:
            print(f"Exception in get_market_data: {e}")
            return []

    async def calculate_floor_prices(self, auth_data: str = "") -> Dict[str, Dict]:
        """Calculate floor prices for all gifts"""
        try:
            # Get filter stats from API
            response = await asyncio.to_thread(
                tonnel_api.get_filter_stats,
                auth_data=auth_data
            )
            
            if response.get("status") == "error":
                print(f"Error getting filter stats: {response.get('error')}")
                return {}
                
            stats_data = response.get("data", {})
            
            # Process the data to get floor prices
            floor_prices = {}
            for key, value in stats_data.items():
                if "_" in key:
                    gift_name, model = key.split("_", 1)
                    if gift_name not in floor_prices:
                        floor_prices[gift_name] = {}
                    
                    floor_prices[gift_name][model] = {
                        "floor_price": value.get("floorprice", 0),
                        "count": value.get("howMany", 0)
                    }
            
            return floor_prices
        except Exception as e:
            print(f"Exception in calculate_floor_prices: {e}")
            return {}

    async def find_arbitrage_opportunities(self, threshold: float = 0.15) -> List[Dict]:
        """Find arbitrage opportunities"""
        opportunities = []
        
        try:
            # Get current market data
            market_data = await self.get_market_data(limit=200)
            
            if not market_data:
                return opportunities
            
            # Group by gift name and model
            gift_groups = {}
            for gift in market_data:
                key = f"{gift.get('name', '')}_{gift.get('model', '')}"
                if key not in gift_groups:
                    gift_groups[key] = []
                gift_groups[key].append(gift)
            
            # Find opportunities within each group
            for key, gifts in gift_groups.items():
                if len(gifts) < 2:
                    continue
                
                # Sort by price
                gifts.sort(key=lambda x: x.get('price', 0))
                
                lowest_price = gifts[0].get('price', 0)
                
                # Calculate average price of top 3-5 listings
                top_prices = [g.get('price', 0) for g in gifts[1:6]]
                if top_prices:
                    avg_price = statistics.mean(top_prices)
                    
                    # Check if there's an arbitrage opportunity
                    profit_margin = (avg_price - lowest_price) / lowest_price
                    
                    if profit_margin >= threshold and lowest_price >= config.MIN_GIFT_PRICE:
                        opportunity = {
                            "gift": gifts[0],
                            "lowest_price": lowest_price,
                            "average_price": avg_price,
                            "profit_margin": profit_margin,
                            "potential_profit": avg_price - lowest_price,
                            "market_depth": len(gifts),
                            "found_at": datetime.now().isoformat()
                        }
                        opportunities.append(opportunity)
            
            # Sort by profit margin
            opportunities.sort(key=lambda x: x["profit_margin"], reverse=True)
            
            return opportunities[:20]  # Return top 20 opportunities
            
        except Exception as e:
            print(f"Exception in find_arbitrage_opportunities: {e}")
            return opportunities

    async def analyze_price_trends(self, gift_name: str, days: int = 7) -> Dict:
        """Analyze price trends for a specific gift"""
        try:
            # Get price history from database
            price_history = await db.get_price_history(gift_name, days)
            
            if len(price_history) < 2:
                return {"error": "Insufficient price data"}
            
            prices = [p["price"] for p in price_history]
            dates = [p["recorded_at"] for p in price_history]
            
            # Calculate statistics
            current_price = prices[0] if prices else 0
            avg_price = statistics.mean(prices)
            min_price = min(prices)
            max_price = max(prices)
            
            # Calculate trend
            if len(prices) >= 2:
                recent_avg = statistics.mean(prices[:len(prices)//3]) if len(prices) >= 3 else prices[0]
                older_avg = statistics.mean(prices[len(prices)//3:]) if len(prices) >= 3 else prices[-1]
                trend = "rising" if recent_avg > older_avg else "falling" if recent_avg < older_avg else "stable"
            else:
                trend = "stable"
            
            return {
                "gift_name": gift_name,
                "current_price": current_price,
                "average_price": avg_price,
                "min_price": min_price,
                "max_price": max_price,
                "price_volatility": (max_price - min_price) / avg_price if avg_price > 0 else 0,
                "trend": trend,
                "data_points": len(prices),
                "period_days": days
            }
            
        except Exception as e:
            print(f"Exception in analyze_price_trends: {e}")
            return {"error": str(e)}

    async def get_undervalued_gifts(self, auth_data: str = "", limit: int = 50) -> List[Dict]:
        """Find potentially undervalued gifts"""
        try:
            # Get floor prices
            floor_prices = await self.calculate_floor_prices(auth_data)
            
            # Get current market listings
            market_data = await self.get_market_data(limit=200)
            
            undervalued = []
            
            for gift in market_data:
                gift_name = gift.get("name", "")
                model = gift.get("model", "")
                current_price = gift.get("price", 0)
                
                if gift_name in floor_prices and model in floor_prices[gift_name]:
                    floor_data = floor_prices[gift_name][model]
                    floor_price = floor_data.get("floor_price", 0)
                    market_count = floor_data.get("count", 0)
                    
                    # Check if current price is significantly below floor
                    if floor_price > 0 and current_price < floor_price * 0.9:  # 10% below floor
                        discount = (floor_price - current_price) / floor_price
                        
                        undervalued.append({
                            "gift": gift,
                            "current_price": current_price,
                            "floor_price": floor_price,
                            "discount": discount,
                            "market_count": market_count,
                            "potential_profit": floor_price - current_price
                        })
            
            # Sort by discount percentage
            undervalued.sort(key=lambda x: x["discount"], reverse=True)
            
            return undervalued[:limit]
            
        except Exception as e:
            print(f"Exception in get_undervalued_gifts: {e}")
            return []

    async def check_watchlist_alerts(self, user_id: int, auth_data: str = "") -> List[Dict]:
        """Check watchlist for price alerts"""
        try:
            watchlist = await db.get_watchlist(user_id)
            alerts = []
            
            for item in watchlist:
                # Get current market data for this item
                gifts = await asyncio.to_thread(
                    tonnel_api.get_gifts,
                    gift_name=item.get("gift_name"),
                    model=item.get("model"),
                    backdrop=item.get("backdrop"),
                    symbol=item.get("symbol"),
                    limit=10,
                    sort="price_asc"
                )
                
                if gifts.get("status") == "error":
                    continue
                    
                market_gifts = gifts.get("data", [])
                
                for gift in market_gifts:
                    current_price = gift.get("price", 0)
                    max_price = item.get("max_price")
                    
                    if max_price and current_price <= max_price:
                        # Check cooldown
                        cooldown_key = f"{user_id}_{gift.get('gift_id')}"
                        last_notification = self.notification_cooldowns.get(cooldown_key)
                        
                        if not last_notification or \
                           datetime.now() - last_notification > timedelta(seconds=config.NOTIFICATION_COOLDOWN):
                            
                            alerts.append({
                                "watchlist_item": item,
                                "gift": gift,
                                "target_price": max_price,
                                "current_price": current_price,
                                "savings": max_price - current_price
                            })
                            
                            # Update cooldown
                            self.notification_cooldowns[cooldown_key] = datetime.now()
            
            return alerts
            
        except Exception as e:
            print(f"Exception in check_watchlist_alerts: {e}")
            return []

    async def get_market_summary(self, auth_data: str = "") -> Dict:
        """Get overall market summary"""
        try:
            # Get recent market data
            market_data = await self.get_market_data(limit=100)
            
            if not market_data:
                return {"error": "No market data available"}
            
            prices = [g.get("price", 0) for g in market_data if g.get("price", 0) > 0]
            
            if not prices:
                return {"error": "No valid price data"}
            
            # Calculate statistics
            total_listings = len(market_data)
            avg_price = statistics.mean(prices)
            median_price = statistics.median(prices)
            min_price = min(prices)
            max_price = max(prices)
            
            # Count by asset
            asset_counts = {}
            for gift in market_data:
                asset = gift.get("asset", "TON")
                asset_counts[asset] = asset_counts.get(asset, 0) + 1
            
            # Get arbitrage opportunities
            opportunities = await self.find_arbitrage_opportunities()
            
            return {
                "total_listings": total_listings,
                "average_price": round(avg_price, 2),
                "median_price": round(median_price, 2),
                "min_price": round(min_price, 2),
                "max_price": round(max_price, 2),
                "asset_distribution": asset_counts,
                "arbitrage_opportunities": len(opportunities),
                "top_opportunity": opportunities[0] if opportunities else None,
                "updated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Exception in get_market_summary: {e}")
            return {"error": str(e)}

# Global arbitrage analyzer instance
arbitrage_analyzer = ArbitrageAnalyzer()
