import os
from dotenv import load_dotenv

load_dotenv()

# Telegram Bot Configuration
BOT_TOKEN = "**********************************************"

# Tonnel API Configuration
TONNEL_API_BASE = "https://gifts2.tonnel.network/api"
TONNEL_GIFTS_ENDPOINT = f"{TONNEL_API_BASE}/pageGifts"
TONNEL_AUCTIONS_ENDPOINT = f"{TONNEL_API_BASE}/pageAuctions"
TONNEL_BUY_ENDPOINT = f"{TONNEL_API_BASE}/buyGift"
TONNEL_SELL_ENDPOINT = f"{TONNEL_API_BASE}/listForSale"
TONNEL_CANCEL_ENDPOINT = f"{TONNEL_API_BASE}/cancelSale"
TONNEL_INFO_ENDPOINT = f"{TONNEL_API_BASE}/userInfo"
TONNEL_WITHDRAW_ENDPOINT = f"{TONNEL_API_BASE}/withdraw"
TONNEL_HISTORY_ENDPOINT = f"{TONNEL_API_BASE}/saleHistory"
TONNEL_FILTER_STATS_ENDPOINT = f"{TONNEL_API_BASE}/filterStats"

# Database Configuration
DATABASE_PATH = "tonnel_bot.db"

# Bot Settings
MAX_GIFTS_PER_PAGE = 30
DEFAULT_PRICE_THRESHOLD = 0.1  # 10% below market price
UPDATE_INTERVAL = 30  # seconds
MAX_NOTIFICATIONS_PER_USER = 50

# Supported Assets
SUPPORTED_ASSETS = ["TON", "USDT", "TONNEL"]

# Sort Options
SORT_OPTIONS = {
    "price_asc": '{"price":1}',
    "price_desc": '{"price":-1}',
    "latest": '{"message_post_time":-1,"gift_id":-1}',
    "mint_time": '{"export_at":-1}',
    "rarity": '{"modelRarity":1,"backdropRarity":1,"symbolRarity":1}',
    "gift_id_asc": '{"gift_id":1}',
    "gift_id_desc": '{"gift_id":-1}'
}

# Auction Sort Options
AUCTION_SORT_OPTIONS = {
    "ending_soon": '{"auction.end_time":1}',
    "latest": '{"auction.start_time":-1}',
    "highest_bid": '{"auction.current_bid":-1}',
    "latest_bid": '{"auction.last_bid_time":-1}'
}

# Admin Settings
ADMIN_IDS = []  # Add admin user IDs here

# Monitoring Settings
ARBITRAGE_THRESHOLD = 0.15  # 15% profit margin
MIN_GIFT_PRICE = 1.0  # Minimum price to consider
MAX_GIFT_PRICE = 1000.0  # Maximum price to consider

# Notification Settings
NOTIFICATION_COOLDOWN = 300  # 5 minutes between same gift notifications
