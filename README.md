# 🎁 Tonnel Trading Bot

Продвинутый Telegram бот для торговли подарками на Tonnel Marketplace с функциями арбитража, аналитики и автоматизации.

## 🚀 Возможности

### 🔍 Поиск и Анализ
- **Поиск подарков** с расширенными фильтрами
- **Арбитражные возможности** в реальном времени
- **Анализ трендов** и истории цен
- **Рыночная аналитика** и статистика

### 📊 Мониторинг
- **Автоматические уведомления** о выгодных сделках
- **Вотчлист** для отслеживания цен
- **Портфолио-трекер** для ваших подарков
- **Ценовые алерты** при достижении целевых цен

### 💰 Торговля
- **Быстрая покупка** подарков
- **Управление лотами** (выставление/отмена)
- **Аукционы** - участие и создание
- **Автоматизация** торговых операций

### 📈 Аналитика
- **Графики цен** и технический анализ
- **Прогнозирование** движения цен
- **Рейтинги** самых прибыльных подарков
- **Отчеты** по портфолио

## 📦 Установка

### Требования
- Python 3.8+
- pip (менеджер пакетов Python)

### Быстрая установка

1. **Клонируйте репозиторий:**
```bash
git clone <repository-url>
cd tonnel-trading-bot
```

2. **Установите зависимости:**
```bash
pip install -r requirements.txt
```

3. **Запустите бота:**
```bash
python main.py
```

### Альтернативная установка

Если у вас проблемы с curl-cffi, установите зависимости по отдельности:

```bash
pip install python-telegram-bot==20.7
pip install curl-cffi==0.6.2
pip install aiofiles aiosqlite python-dotenv
pip install numpy pandas matplotlib seaborn Pillow
```

## 🔧 Настройка

### Конфигурация бота

Основные настройки находятся в файле `config.py`:

- `BOT_TOKEN` - токен вашего Telegram бота
- `UPDATE_INTERVAL` - интервал обновления данных (секунды)
- `ARBITRAGE_THRESHOLD` - минимальный процент прибыли для арбитража
- `MAX_GIFTS_PER_PAGE` - максимум подарков на страницу

### Получение Auth Data

Для работы с вашим аккаунтом Tonnel:

1. Зайдите на [market.tonnel.network](https://market.tonnel.network)
2. Войдите в свой аккаунт
3. Откройте консоль браузера (F12)
4. Перейдите: Application → Local Storage → web-initData
5. Скопируйте значение
6. В боте: `/auth ВАШИ_ДАННЫЕ`

## 🎮 Использование

### Основные команды

- `/start` - Запуск бота и главное меню
- `/help` - Справка по всем командам
- `/auth` - Настройка авторизации

### Поиск и анализ

- `/search toy bear` - Поиск подарков Toy Bear
- `/arbitrage` - Поиск арбитражных возможностей
- `/market` - Обзор рынка
- `/trends lunar snake` - Анализ трендов Lunar Snake

### Управление аккаунтом

- `/balance` - Проверка баланса
- `/mygifts` - Ваши подарки
- `/watchlist` - Управление вотчлистом

### Аукционы

- `/auctions` - Поиск аукционов

## 💡 Примеры использования

### Поиск выгодных сделок
```
/arbitrage
```
Бот найдет подарки, которые можно купить дешево и продать дороже.

### Отслеживание цены
```
/search winter wreath
```
Затем добавьте в вотчлист через кнопку "⭐ Добавить в вотчлист".

### Анализ трендов
```
/trends toy bear
```
Получите детальный анализ движения цен за последние дни.

## 🔔 Уведомления

Бот автоматически отправляет уведомления о:

- 🚀 **Арбитражных возможностях** с высокой прибылью
- ⭐ **Срабатывании вотчлиста** при достижении целевых цен
- 📉 **Значительных падениях цен** на интересные подарки
- 🆕 **Новых лотах** по вашим критериям

## 📊 Функции аналитики

### Рыночная статистика
- Общее количество лотов
- Средние и медианные цены
- Распределение по валютам
- Топ подарков по популярности

### Анализ трендов
- История цен за выбранный период
- Уровни поддержки и сопротивления
- Прогнозы движения цен
- Индикаторы волатильности

### Портфолио-анализ
- Общая стоимость портфолио
- Распределение по активам
- Топ холдингов
- Показатели диверсификации

## 🛡️ Безопасность

- **Данные авторизации** хранятся локально в зашифрованном виде
- **API запросы** выполняются через защищенные соединения
- **Личная информация** не передается третьим лицам
- **Логи** содержат только техническую информацию

## 🔧 Техническая информация

### Архитектура
- **Основной бот** - обработка команд и интерфейс
- **API модуль** - взаимодействие с Tonnel Marketplace
- **Модуль арбитража** - поиск торговых возможностей
- **Система мониторинга** - отслеживание рынка
- **База данных** - хранение пользовательских данных
- **Аналитика** - расчет метрик и прогнозов

### База данных
Используется SQLite для хранения:
- Данные пользователей
- Вотчлисты
- История уведомлений
- История цен
- Настройки

### API интеграция
Полная интеграция с Tonnel Marketplace API:
- Поиск подарков и аукционов
- Покупка и продажа
- Управление балансом
- Получение статистики

## 🚨 Устранение неполадок

### Ошибки установки
```bash
# Если проблемы с curl-cffi на Windows:
pip install --upgrade pip
pip install curl-cffi --no-cache-dir

# Если проблемы с matplotlib:
pip install matplotlib --upgrade
```

### Ошибки авторизации
- Проверьте правильность копирования auth data
- Убедитесь, что вы вошли в аккаунт на market.tonnel.network
- Попробуйте получить новые данные авторизации

### Проблемы с уведомлениями
- Проверьте настройки уведомлений: `/settings`
- Убедитесь, что бот не заблокирован в Telegram
- Проверьте интернет-соединение

## 📞 Поддержка

При возникновении проблем:

1. Проверьте логи в файле `tonnel_bot.log`
2. Убедитесь, что все зависимости установлены
3. Проверьте правильность настройки auth data
4. Перезапустите бота

## 🎯 Стратегии заработка

### Арбитраж
1. Используйте `/arbitrage` для поиска возможностей
2. Настройте уведомления для быстрого реагирования
3. Изучите рынок перед крупными покупками

### Долгосрочные инвестиции
1. Анализируйте тренды с помощью `/trends`
2. Покупайте недооцененные подарки
3. Отслеживайте портфолио

### Спекулятивная торговля
1. Следите за новостями и событиями
2. Используйте технический анализ
3. Устанавливайте стоп-лоссы

## 📈 Планы развития

- [ ] Машинное обучение для прогнозов
- [ ] Интеграция с другими маркетплейсами
- [ ] Мобильное приложение
- [ ] Социальные функции и рейтинги
- [ ] Расширенная автоматизация

---

**⚠️ Дисклеймер:** Торговля подарками связана с рисками. Бот предоставляет инструменты для анализа, но не гарантирует прибыль. Торгуйте ответственно!
