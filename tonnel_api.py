import json
import asyncio
from typing import Dict, List, Optional, Union
from curl_cffi import requests
import config

class TonnelAPI:
    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'origin': 'https://market.tonnel.network',
            'referer': 'https://market.tonnel.network/',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        }

    def _make_request(self, url: str, json_data: dict) -> dict:
        """Make HTTP request with error handling"""
        try:
            response = self.session.post(
                url, 
                json=json_data, 
                headers=self.headers,
                impersonate="chrome",
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e), "status": "error"}

    def get_gifts(self, 
                  gift_name: str = None,
                  model: str = None,
                  backdrop: str = None,
                  symbol: str = None,
                  gift_num: int = None,
                  page: int = 1,
                  limit: int = 30,
                  sort: str = "price_asc",
                  price_range: List[Union[int, float]] = None,
                  asset: str = "TON",
                  auth_data: str = "") -> dict:
        """Get gifts with filters"""
        
        # Build filter
        filter_data = {
            "price": {"$exists": True},
            "refunded": {"$ne": True},
            "buyer": {"$exists": False},
            "export_at": {"$exists": True},
            "asset": asset
        }
        
        if gift_name:
            filter_data["gift_name"] = gift_name
        if model:
            if "(" in model and "%" in model:
                filter_data["model"] = model
            else:
                filter_data["model"] = {"$regex": f"^{model} \\("}
        if backdrop:
            if "(" in backdrop and "%" in backdrop:
                filter_data["backdrop"] = backdrop
            else:
                filter_data["backdrop"] = {"$regex": f"^{backdrop} \\("}
        if symbol:
            if "(" in symbol and "%" in symbol:
                filter_data["symbol"] = symbol
            else:
                filter_data["symbol"] = {"$regex": f"^{symbol} \\("}
        if gift_num:
            filter_data["gift_num"] = gift_num

        json_data = {
            'page': page,
            'limit': min(limit, config.MAX_GIFTS_PER_PAGE),
            'sort': config.SORT_OPTIONS.get(sort, config.SORT_OPTIONS["price_asc"]),
            'filter': json.dumps(filter_data),
            'price_range': price_range,
            'user_auth': auth_data,
        }

        return self._make_request(config.TONNEL_GIFTS_ENDPOINT, json_data)

    def get_auctions(self,
                     gift_name: str = None,
                     model: str = None,
                     backdrop: str = None,
                     symbol: str = None,
                     gift_num: int = None,
                     page: int = 1,
                     limit: int = 30,
                     sort: str = "ending_soon",
                     price_range: List[Union[int, float]] = None,
                     asset: str = "TON",
                     auth_data: str = "") -> dict:
        """Get auctions with filters"""
        
        filter_data = {
            "auction": {"$exists": True},
            "asset": asset
        }
        
        if gift_name:
            filter_data["gift_name"] = gift_name
        if model:
            if "(" in model and "%" in model:
                filter_data["model"] = model
            else:
                filter_data["model"] = {"$regex": f"^{model} \\("}
        if backdrop:
            if "(" in backdrop and "%" in backdrop:
                filter_data["backdrop"] = backdrop
            else:
                filter_data["backdrop"] = {"$regex": f"^{backdrop} \\("}
        if symbol:
            if "(" in symbol and "%" in symbol:
                filter_data["symbol"] = symbol
            else:
                filter_data["symbol"] = {"$regex": f"^{symbol} \\("}
        if gift_num:
            filter_data["gift_num"] = gift_num

        json_data = {
            'page': page,
            'limit': min(limit, config.MAX_GIFTS_PER_PAGE),
            'sort': config.AUCTION_SORT_OPTIONS.get(sort, config.AUCTION_SORT_OPTIONS["ending_soon"]),
            'filter': json.dumps(filter_data),
            'price_range': price_range,
            'user_auth': auth_data,
        }

        return self._make_request(config.TONNEL_AUCTIONS_ENDPOINT, json_data)

    def get_user_info(self, auth_data: str) -> dict:
        """Get user account information"""
        json_data = {'user_auth': auth_data}
        try:
            response = self._make_request(config.TONNEL_INFO_ENDPOINT, json_data)
            print(f"User info response: {response}")
            return response
        except Exception as e:
            print(f"Error getting user info: {e}")
            return {"error": str(e), "status": "error"}

    def buy_gift(self, gift_id: int, price: float, auth_data: str, 
                 receiver: int = None, anonymously: bool = False, 
                 show_price: bool = False) -> dict:
        """Buy a gift"""
        json_data = {
            'gift_id': gift_id,
            'price': price,
            'user_auth': auth_data,
            'anonymously': anonymously,
            'showPrice': show_price
        }
        if receiver:
            json_data['receiver'] = receiver
            
        return self._make_request(config.TONNEL_BUY_ENDPOINT, json_data)

    def list_for_sale(self, gift_id: int, price: float, auth_data: str) -> dict:
        """List gift for sale"""
        json_data = {
            'gift_id': gift_id,
            'price': price,
            'user_auth': auth_data
        }
        return self._make_request(config.TONNEL_SELL_ENDPOINT, json_data)

    def cancel_sale(self, gift_id: int, auth_data: str) -> dict:
        """Cancel gift sale"""
        json_data = {
            'gift_id': gift_id,
            'user_auth': auth_data
        }
        return self._make_request(config.TONNEL_CANCEL_ENDPOINT, json_data)

    def get_sale_history(self, auth_data: str, page: int = 1, limit: int = 50,
                        type_filter: str = "ALL", gift_name: str = None,
                        model: str = None, backdrop: str = None,
                        sort: str = "latest") -> dict:
        """Get sale history"""
        filter_data = {}
        if gift_name:
            filter_data["gift_name"] = gift_name
        if model:
            filter_data["model"] = model
        if backdrop:
            filter_data["backdrop"] = backdrop

        json_data = {
            'page': page,
            'limit': min(limit, 50),
            'type': type_filter,
            'sort': sort,
            'filter': json.dumps(filter_data) if filter_data else "{}",
            'user_auth': auth_data
        }
        return self._make_request(config.TONNEL_HISTORY_ENDPOINT, json_data)

    def get_filter_stats(self, auth_data: str) -> dict:
        """Get filter statistics"""
        json_data = {'user_auth': auth_data}
        return self._make_request(config.TONNEL_FILTER_STATS_ENDPOINT, json_data)

    def withdraw(self, wallet: str, auth_data: str, amount: float, asset: str = "TON") -> dict:
        """Withdraw funds"""
        json_data = {
            'wallet': wallet,
            'amount': amount,
            'asset': asset,
            'user_auth': auth_data
        }
        return self._make_request(config.TONNEL_WITHDRAW_ENDPOINT, json_data)

    def my_gifts(self, listed: bool = True, page: int = 1, limit: int = 30, auth_data: str = "") -> dict:
        """Get user's gifts"""
        json_data = {
            'listed': listed,
            'page': page,
            'limit': min(limit, config.MAX_GIFTS_PER_PAGE),
            'user_auth': auth_data
        }
        return self._make_request(f"{config.TONNEL_API_BASE}/myGifts", json_data)

    def create_auction(self, gift_id: int, starting_bid: float, auth_data: str, duration: int = 1) -> dict:
        """Create auction"""
        json_data = {
            'gift_id': gift_id,
            'starting_bid': starting_bid,
            'duration': duration,
            'user_auth': auth_data
        }
        return self._make_request(f"{config.TONNEL_API_BASE}/createAuction", json_data)

    def cancel_auction(self, auction_id: str, auth_data: str) -> dict:
        """Cancel auction"""
        json_data = {
            'auction_id': auction_id,
            'user_auth': auth_data
        }
        return self._make_request(f"{config.TONNEL_API_BASE}/cancelAuction", json_data)

    def place_bid(self, auction_id: str, amount: float, auth_data: str, asset: str = "TON") -> dict:
        """Place bid on auction"""
        json_data = {
            'auction_id': auction_id,
            'amount': amount,
            'asset': asset,
            'user_auth': auth_data
        }
        return self._make_request(f"{config.TONNEL_API_BASE}/placeBid", json_data)

# Global API instance
tonnel_api = TonnelAPI()
