import asyncio
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from io import BytesIO
import numpy as np

from database import db
from tonnel_api import tonnel_api
from arbitrage import arbitrage_analyzer

class MarketAnalytics:
    def __init__(self):
        self.price_cache = {}
        self.trend_cache = {}

    async def analyze_gift_performance(self, gift_name: str, days: int = 30) -> Dict:
        """Analyze gift performance over time"""
        try:
            # Get price history
            price_history = await db.get_price_history(gift_name, days)
            
            if len(price_history) < 2:
                return {"error": "Insufficient data for analysis"}
            
            # Convert to DataFrame for easier analysis
            df = pd.DataFrame(price_history)
            df['recorded_at'] = pd.to_datetime(df['recorded_at'])
            df = df.sort_values('recorded_at')
            
            # Calculate metrics
            current_price = df['price'].iloc[-1]
            avg_price = df['price'].mean()
            min_price = df['price'].min()
            max_price = df['price'].max()
            
            # Price volatility
            volatility = df['price'].std() / avg_price if avg_price > 0 else 0
            
            # Trend analysis
            if len(df) >= 7:
                recent_avg = df['price'].tail(7).mean()
                older_avg = df['price'].head(7).mean()
                trend_direction = "rising" if recent_avg > older_avg else "falling" if recent_avg < older_avg else "stable"
                trend_strength = abs(recent_avg - older_avg) / older_avg if older_avg > 0 else 0
            else:
                trend_direction = "stable"
                trend_strength = 0
            
            # Support and resistance levels
            support_level = df['price'].quantile(0.2)
            resistance_level = df['price'].quantile(0.8)
            
            # Trading volume (number of price records as proxy)
            daily_volume = df.groupby(df['recorded_at'].dt.date).size().mean()
            
            return {
                "gift_name": gift_name,
                "analysis_period": days,
                "current_price": round(current_price, 4),
                "average_price": round(avg_price, 4),
                "min_price": round(min_price, 4),
                "max_price": round(max_price, 4),
                "volatility": round(volatility, 4),
                "trend_direction": trend_direction,
                "trend_strength": round(trend_strength, 4),
                "support_level": round(support_level, 4),
                "resistance_level": round(resistance_level, 4),
                "daily_volume": round(daily_volume, 2),
                "data_points": len(df),
                "price_change_24h": self._calculate_price_change(df, 1),
                "price_change_7d": self._calculate_price_change(df, 7),
                "price_change_30d": self._calculate_price_change(df, 30)
            }
            
        except Exception as e:
            return {"error": str(e)}

    def _calculate_price_change(self, df: pd.DataFrame, days: int) -> float:
        """Calculate price change over specified days"""
        try:
            if len(df) < 2:
                return 0
            
            cutoff_date = df['recorded_at'].max() - timedelta(days=days)
            recent_data = df[df['recorded_at'] >= cutoff_date]
            
            if len(recent_data) < 2:
                return 0
            
            old_price = recent_data['price'].iloc[0]
            new_price = recent_data['price'].iloc[-1]
            
            return ((new_price - old_price) / old_price * 100) if old_price > 0 else 0
            
        except Exception:
            return 0

    async def generate_price_chart(self, gift_name: str, days: int = 7) -> Optional[BytesIO]:
        """Generate price chart for a gift"""
        try:
            price_history = await db.get_price_history(gift_name, days)
            
            if len(price_history) < 2:
                return None
            
            # Prepare data
            df = pd.DataFrame(price_history)
            df['recorded_at'] = pd.to_datetime(df['recorded_at'])
            df = df.sort_values('recorded_at')
            
            # Create chart
            plt.figure(figsize=(12, 6))
            plt.style.use('seaborn-v0_8')
            
            # Price line
            plt.plot(df['recorded_at'], df['price'], linewidth=2, color='#2E86AB', label='Price')
            
            # Moving average
            if len(df) >= 5:
                df['ma'] = df['price'].rolling(window=min(5, len(df))).mean()
                plt.plot(df['recorded_at'], df['ma'], linewidth=1, color='#A23B72', alpha=0.7, label='Moving Average')
            
            # Formatting
            plt.title(f'{gift_name} - Price History ({days} days)', fontsize=16, fontweight='bold')
            plt.xlabel('Date', fontsize=12)
            plt.ylabel('Price (TON)', fontsize=12)
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Save to BytesIO
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
            buffer.seek(0)
            plt.close()
            
            return buffer
            
        except Exception as e:
            print(f"Error generating chart: {e}")
            return None

    async def find_trending_gifts(self, days: int = 7) -> List[Dict]:
        """Find trending gifts based on price movement and volume"""
        try:
            # Get recent price data
            all_price_data = await db.get_price_history(days=days)
            
            if not all_price_data:
                return []
            
            # Group by gift name
            gift_groups = {}
            for record in all_price_data:
                gift_name = record['gift_name']
                if gift_name not in gift_groups:
                    gift_groups[gift_name] = []
                gift_groups[gift_name].append(record)
            
            trending = []
            
            for gift_name, records in gift_groups.items():
                if len(records) < 3:  # Need minimum data points
                    continue
                
                df = pd.DataFrame(records)
                df['recorded_at'] = pd.to_datetime(df['recorded_at'])
                df = df.sort_values('recorded_at')
                
                # Calculate trend metrics
                prices = df['price'].values
                volume = len(records)  # Number of price updates as volume proxy
                
                # Price change
                price_change = ((prices[-1] - prices[0]) / prices[0] * 100) if prices[0] > 0 else 0
                
                # Volatility
                volatility = np.std(prices) / np.mean(prices) if np.mean(prices) > 0 else 0
                
                # Trend score (combination of price change, volume, and volatility)
                trend_score = abs(price_change) * (volume / 10) * (1 + volatility)
                
                trending.append({
                    "gift_name": gift_name,
                    "price_change": round(price_change, 2),
                    "volume": volume,
                    "volatility": round(volatility, 4),
                    "trend_score": round(trend_score, 2),
                    "current_price": round(prices[-1], 4),
                    "direction": "up" if price_change > 0 else "down" if price_change < 0 else "stable"
                })
            
            # Sort by trend score
            trending.sort(key=lambda x: x["trend_score"], reverse=True)
            
            return trending[:10]  # Top 10 trending
            
        except Exception as e:
            print(f"Error finding trending gifts: {e}")
            return []

    async def calculate_market_metrics(self, auth_data: str = "") -> Dict:
        """Calculate overall market metrics"""
        try:
            # Get current market data
            market_data = await arbitrage_analyzer.get_market_data(limit=200)
            
            if not market_data:
                return {"error": "No market data available"}
            
            # Basic metrics
            total_listings = len(market_data)
            prices = [g.get("price", 0) for g in market_data if g.get("price", 0) > 0]
            
            if not prices:
                return {"error": "No valid price data"}
            
            # Price statistics
            avg_price = statistics.mean(prices)
            median_price = statistics.median(prices)
            std_price = statistics.stdev(prices) if len(prices) > 1 else 0
            
            # Market concentration (Gini coefficient approximation)
            sorted_prices = sorted(prices)
            n = len(sorted_prices)
            cumsum = np.cumsum(sorted_prices)
            gini = (2 * sum((i + 1) * price for i, price in enumerate(sorted_prices))) / (n * sum(sorted_prices)) - (n + 1) / n
            
            # Asset distribution
            asset_counts = {}
            total_value_by_asset = {}
            
            for gift in market_data:
                asset = gift.get("asset", "TON")
                price = gift.get("price", 0)
                
                asset_counts[asset] = asset_counts.get(asset, 0) + 1
                total_value_by_asset[asset] = total_value_by_asset.get(asset, 0) + price
            
            # Gift name distribution
            gift_counts = {}
            for gift in market_data:
                name = gift.get("name", "Unknown")
                gift_counts[name] = gift_counts.get(name, 0) + 1
            
            # Top gifts by listing count
            top_gifts = sorted(gift_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return {
                "total_listings": total_listings,
                "price_statistics": {
                    "average": round(avg_price, 4),
                    "median": round(median_price, 4),
                    "std_deviation": round(std_price, 4),
                    "min": round(min(prices), 4),
                    "max": round(max(prices), 4)
                },
                "market_concentration": round(gini, 4),
                "asset_distribution": {
                    "counts": asset_counts,
                    "total_values": {k: round(v, 2) for k, v in total_value_by_asset.items()}
                },
                "top_gifts": top_gifts,
                "updated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

    async def predict_price_movement(self, gift_name: str, days: int = 7) -> Dict:
        """Simple price movement prediction based on historical data"""
        try:
            price_history = await db.get_price_history(gift_name, days * 2)  # Get more data for better prediction
            
            if len(price_history) < 5:
                return {"error": "Insufficient data for prediction"}
            
            df = pd.DataFrame(price_history)
            df['recorded_at'] = pd.to_datetime(df['recorded_at'])
            df = df.sort_values('recorded_at')
            
            prices = df['price'].values
            
            # Simple linear regression for trend
            x = np.arange(len(prices))
            coeffs = np.polyfit(x, prices, 1)
            trend_slope = coeffs[0]
            
            # Calculate momentum indicators
            if len(prices) >= 5:
                sma_5 = np.mean(prices[-5:])
                sma_10 = np.mean(prices[-10:]) if len(prices) >= 10 else sma_5
                
                momentum = (sma_5 - sma_10) / sma_10 if sma_10 > 0 else 0
            else:
                momentum = 0
            
            # Volatility
            volatility = np.std(prices[-7:]) if len(prices) >= 7 else np.std(prices)
            
            # Simple prediction
            current_price = prices[-1]
            predicted_change = trend_slope * 3  # Predict 3 periods ahead
            predicted_price = current_price + predicted_change
            
            # Confidence based on volatility (lower volatility = higher confidence)
            confidence = max(0, min(1, 1 - (volatility / current_price)))
            
            # Direction prediction
            if abs(predicted_change) < volatility * 0.1:
                direction = "stable"
            elif predicted_change > 0:
                direction = "bullish"
            else:
                direction = "bearish"
            
            return {
                "gift_name": gift_name,
                "current_price": round(current_price, 4),
                "predicted_price": round(predicted_price, 4),
                "predicted_change": round(predicted_change, 4),
                "predicted_change_percent": round((predicted_change / current_price * 100), 2),
                "direction": direction,
                "confidence": round(confidence, 2),
                "momentum": round(momentum, 4),
                "volatility": round(volatility, 4),
                "data_points": len(prices),
                "prediction_horizon": "3 periods"
            }
            
        except Exception as e:
            return {"error": str(e)}

    async def get_portfolio_analysis(self, user_id: int, auth_data: str) -> Dict:
        """Analyze user's gift portfolio"""
        try:
            # Get user's gifts
            my_gifts_response = await asyncio.to_thread(
                tonnel_api.my_gifts,
                listed=False,
                auth_data=auth_data
            )
            
            if my_gifts_response.get("status") == "error":
                return {"error": "Could not fetch portfolio data"}
            
            gifts = my_gifts_response.get("data", [])
            
            if not gifts:
                return {"message": "No gifts in portfolio"}
            
            # Calculate portfolio metrics
            total_value = 0
            gift_counts = {}
            asset_distribution = {}
            
            for gift in gifts:
                # Get current market price (simplified - would need more complex logic)
                price = gift.get("price", 0)  # This might not be current market price
                asset = gift.get("asset", "TON")
                name = gift.get("name", "Unknown")
                
                total_value += price
                gift_counts[name] = gift_counts.get(name, 0) + 1
                asset_distribution[asset] = asset_distribution.get(asset, 0) + price
            
            # Top holdings
            top_holdings = sorted(gift_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            return {
                "total_gifts": len(gifts),
                "estimated_total_value": round(total_value, 4),
                "asset_distribution": {k: round(v, 4) for k, v in asset_distribution.items()},
                "top_holdings": top_holdings,
                "diversification_score": len(gift_counts) / len(gifts) if gifts else 0,
                "updated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}

# Global analytics instance
market_analytics = MarketAnalytics()
